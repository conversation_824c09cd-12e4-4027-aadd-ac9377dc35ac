<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.out -mbq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.map -iC:/ti/mspm0_sdk_2_05_00_05/source -iC:/Users/<USER>/workspace_ccstheia/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang_linkInfo.xml --rom_model ./App_task.o ./ti_msp_dl_config.o ./startup_mspm0g351x_ticlang.o ./main.o ./BQ769x2_Configs/BQ769x2_protocol.o ./Communications/CAN_communication.o ./Communications/I2C_communication.o ./Communications/UART_communication.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x684f9ee2</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x6411</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>App_task.o</file>
         <name>App_task.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g351x_ticlang.o</file>
         <name>startup_mspm0g351x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\BQ769x2_Configs\</path>
         <kind>object</kind>
         <file>BQ769x2_protocol.o</file>
         <name>BQ769x2_protocol.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\Communications\</path>
         <kind>object</kind>
         <file>CAN_communication.o</file>
         <name>CAN_communication.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\Communications\</path>
         <kind>object</kind>
         <file>I2C_communication.o</file>
         <name>I2C_communication.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.\Communications\</path>
         <kind>object</kind>
         <file>UART_communication.o</file>
         <name>UART_communication.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_mcan.o</name>
      </input_file>
      <input_file id="fl-1a">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-1b">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-1c">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0gx51x.o</name>
      </input_file>
      <input_file id="fl-1d">
         <path>C:\ti\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0gx51x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_factoryregion.o</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_powf.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrtf.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbnf.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsidf.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-1a">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.BQ769x2_BOT_Init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x1a30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.BQ769x2_TOP_Init</name>
         <load_address>0x1af0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1af0</run_address>
         <size>0x1864</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.BatteryDataUpdate_32s</name>
         <load_address>0x3354</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3354</run_address>
         <size>0x218</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.text.DL_MCAN_msgRAMConfig</name>
         <load_address>0x356c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x356c</run_address>
         <size>0x214</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.I2C_ReadReg</name>
         <load_address>0x3780</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3780</run_address>
         <size>0x1a0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.CAN_ID_Init_on_Startup</name>
         <load_address>0x3920</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3920</run_address>
         <size>0x160</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.text.CANTxMsgSendPoll</name>
         <load_address>0x3a80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3a80</run_address>
         <size>0x148</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-157">
         <name>.text.CAN_Write</name>
         <load_address>0x3bc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3bc8</run_address>
         <size>0x148</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x3d10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3d10</run_address>
         <size>0x148</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.RS485_Send</name>
         <load_address>0x3e58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3e58</run_address>
         <size>0x144</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.BMU_Normal_Mode</name>
         <load_address>0x3f9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f9c</run_address>
         <size>0x124</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.SYSCFG_DL_MCAN0_init</name>
         <load_address>0x40c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x40c0</run_address>
         <size>0x11c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.text.SYSCFG_DL_MCAN1_init</name>
         <load_address>0x41dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x41dc</run_address>
         <size>0x110</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.text.DL_MCAN_readMsgRam</name>
         <load_address>0x42ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x42ec</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.__divdf3</name>
         <load_address>0x43f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x43f8</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-182">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x4504</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4504</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.text.check_signal_pattern</name>
         <load_address>0x4608</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4608</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-179">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x470c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x470c</run_address>
         <size>0xf8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_MCAN_writeMsgRam</name>
         <load_address>0x4804</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4804</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-158">
         <name>.text.BQ769x2_ReadPassQ</name>
         <load_address>0x48f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x48f8</run_address>
         <size>0xec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x49e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x49e4</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.text.DL_MCAN_config</name>
         <load_address>0x4acc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4acc</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.__muldf3</name>
         <load_address>0x4bb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4bb0</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.text.DL_MCAN_init</name>
         <load_address>0x4c94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4c94</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.text.DL_MCAN_setBitTime</name>
         <load_address>0x4d70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4d70</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.text</name>
         <load_address>0x4e48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e48</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-47">
         <name>.text.I2C0_IRQHandler</name>
         <load_address>0x4f20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4f20</run_address>
         <size>0xd4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.CANprocessCANRxMsg</name>
         <load_address>0x4ff4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4ff4</run_address>
         <size>0xcc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.BMU_Standby_Mode</name>
         <load_address>0x50c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x50c0</run_address>
         <size>0xb8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.I2C_WriteReg</name>
         <load_address>0x5178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5178</run_address>
         <size>0xac</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-41">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x5224</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5224</run_address>
         <size>0xac</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.text.CRC8</name>
         <load_address>0x52d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x52d0</run_address>
         <size>0xa4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text:memcpy</name>
         <load_address>0x5374</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5374</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-35">
         <name>.text.Default_Handler</name>
         <load_address>0x540e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x540e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x5410</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5410</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.BQ769x2_ReadSafetyStatus</name>
         <load_address>0x54a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x54a8</run_address>
         <size>0x94</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.SYSCFG_DL_PWM_0_init</name>
         <load_address>0x553c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x553c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.text.__mulsf3</name>
         <load_address>0x55cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x55cc</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-173">
         <name>.text.DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <load_address>0x5658</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5658</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x56e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x56e0</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.delayUS</name>
         <load_address>0x5768</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5768</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.BMU_Shutdown_Mode</name>
         <load_address>0x57f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x57f0</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.main</name>
         <load_address>0x5874</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5874</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.text:decompress:lzss</name>
         <load_address>0x58f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x58f8</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x5974</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5974</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-119">
         <name>.text.__gedf2</name>
         <load_address>0x59e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x59e8</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.text.BQ769x2_ReadAllCellVoltage</name>
         <load_address>0x5a5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5a5c</run_address>
         <size>0x68</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.__ledf2</name>
         <load_address>0x5ac4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ac4</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.__aeabi_dcmp</name>
         <load_address>0x5b2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b2c</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text:memset</name>
         <load_address>0x5b8e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b8e</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-123"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.Variables_Init</name>
         <load_address>0x5bf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5bf0</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x5c50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5c50</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x5cae</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5cae</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ee">
         <name>.text.DL_FactoryRegion_initTrimTable</name>
         <load_address>0x5cb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5cb0</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x5d08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d08</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.SYSCFG_DL_I2C_0_init</name>
         <load_address>0x5d60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d60</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.DL_UART_fillTXFIFO</name>
         <load_address>0x5db8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5db8</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x5e0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5e0c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.text.BQ769x2_ReadCurrent</name>
         <load_address>0x5e58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5e58</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text.DL_UART_init</name>
         <load_address>0x5ea0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ea0</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-40">
         <name>.text.CANFD1_IRQHandler</name>
         <load_address>0x5ee8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ee8</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.DL_MCAN_getRevisionId</name>
         <load_address>0x5f2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f2c</run_address>
         <size>0x44</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x5f70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f70</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.Gpio_Init</name>
         <load_address>0x5fb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5fb4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x5ff8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ff8</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.text.CANFD0_IRQHandler</name>
         <load_address>0x603c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x603c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x607c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x607c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.text.DL_MCAN_writeRxFIFOAck</name>
         <load_address>0x60bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60bc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x60fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60fc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.text.DL_I2C_flushControllerTXFIFO</name>
         <load_address>0x613c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x613c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.text.__floatsisf</name>
         <load_address>0x6178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6178</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x61b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x61b4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.__muldsi3</name>
         <load_address>0x61f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x61f0</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1f9">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x622a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x622a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.BMU_Ship_Mode</name>
         <load_address>0x622c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x622c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text.DL_MCAN_getRxFIFOStatus</name>
         <load_address>0x6264</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6264</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_MCAN_TXBufAddReq</name>
         <load_address>0x629c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x629c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text.DL_MCAN_setExtIDAndMask</name>
         <load_address>0x62d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x62d0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text.TIMA0_IRQHandler</name>
         <load_address>0x6304</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6304</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-36">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x6338</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6338</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.CommandSubcommands</name>
         <load_address>0x6368</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6368</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text.DL_MCAN_addStdMsgIDFilter</name>
         <load_address>0x6394</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6394</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.BQ769x2_ReadBatteryStatus</name>
         <load_address>0x63c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x63c0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.text.__floatunsisf</name>
         <load_address>0x63e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x63e8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-68">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x6410</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6410</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x6438</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6438</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-198">
         <name>.text.DL_MCAN_setClockConfig</name>
         <load_address>0x6460</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6460</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.__floatunsidf</name>
         <load_address>0x6484</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6484</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.DL_UART_receiveDataBlocking</name>
         <load_address>0x64a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x64a8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.CANTxMsgSendParamInitDefault</name>
         <load_address>0x64c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x64c8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.text.DL_MCAN_enableIntr</name>
         <load_address>0x64e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x64e4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.text.DL_MCAN_enableIntrLine</name>
         <load_address>0x6500</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6500</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.text.DL_MCAN_selectIntrLine</name>
         <load_address>0x651c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x651c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x6538</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6538</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x6554</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6554</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.text.DL_MCAN_setOpMode</name>
         <load_address>0x6570</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6570</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x6588</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6588</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x65a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65a0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text.DL_MCAN_clearIntrStatus</name>
         <load_address>0x65b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65b8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x65cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65cc</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-22">
         <name>.text:decompress:none</name>
         <load_address>0x65de</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65de</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_MCAN_getOpMode</name>
         <load_address>0x65f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65f0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.text.DL_MCAN_isMemInitDone</name>
         <load_address>0x6600</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6600</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x6610</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6610</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-77">
         <name>.text.DL_MCAN_getIntrStatus</name>
         <load_address>0x6620</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6620</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.__aeabi_memclr</name>
         <load_address>0x662c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x662c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-119"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x6638</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6638</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-58">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x6644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6644</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text:abort</name>
         <load_address>0x664c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x664c</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.HOSTexit</name>
         <load_address>0x6652</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6652</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-31">
         <name>.text.Reset_Handler</name>
         <load_address>0x6656</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6656</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text._system_pre_init</name>
         <load_address>0x665a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x665a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-24e">
         <name>.cinit..data.load</name>
         <load_address>0x6960</load_address>
         <readonly>true</readonly>
         <run_address>0x6960</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-24c">
         <name>__TI_handler_table</name>
         <load_address>0x69d0</load_address>
         <readonly>true</readonly>
         <run_address>0x69d0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-250">
         <name>.cinit..TrimTable.load</name>
         <load_address>0x69dc</load_address>
         <readonly>true</readonly>
         <run_address>0x69dc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-24f">
         <name>.cinit..bss.load</name>
         <load_address>0x69e4</load_address>
         <readonly>true</readonly>
         <run_address>0x69e4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-24d">
         <name>__TI_cinit_table</name>
         <load_address>0x69ec</load_address>
         <readonly>true</readonly>
         <run_address>0x69ec</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-202">
         <name>.rodata.CANDLC_Coding</name>
         <load_address>0x6660</load_address>
         <readonly>true</readonly>
         <run_address>0x6660</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.rodata.gMCAN0MsgRAMConfigParams</name>
         <load_address>0x66e0</load_address>
         <readonly>true</readonly>
         <run_address>0x66e0</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.rodata.gMCAN1MsgRAMConfigParams</name>
         <load_address>0x6740</load_address>
         <readonly>true</readonly>
         <run_address>0x6740</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.rodata..L__const.DL_MCAN_getDataSize.dataSize</name>
         <load_address>0x67a0</load_address>
         <readonly>true</readonly>
         <run_address>0x67a0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.rodata.gMCAN0InitParams</name>
         <load_address>0x67e0</load_address>
         <readonly>true</readonly>
         <run_address>0x67e0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.rodata.gMCAN1InitParams</name>
         <load_address>0x6814</load_address>
         <readonly>true</readonly>
         <run_address>0x6814</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.rodata.gMCAN0ConfigParams</name>
         <load_address>0x6848</load_address>
         <readonly>true</readonly>
         <run_address>0x6848</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x6874</load_address>
         <readonly>true</readonly>
         <run_address>0x6874</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.rodata.cst32</name>
         <load_address>0x689c</load_address>
         <readonly>true</readonly>
         <run_address>0x689c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.rodata.gMCAN0BitTimes</name>
         <load_address>0x68bc</load_address>
         <readonly>true</readonly>
         <run_address>0x68bc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.rodata.gMCAN1BitTimes</name>
         <load_address>0x68dc</load_address>
         <readonly>true</readonly>
         <run_address>0x68dc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x68fc</load_address>
         <readonly>true</readonly>
         <run_address>0x68fc</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.rodata.gMCAN0StdFiltelem</name>
         <load_address>0x6910</load_address>
         <readonly>true</readonly>
         <run_address>0x6910</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.rodata.gMCAN1StdFiltelem</name>
         <load_address>0x6920</load_address>
         <readonly>true</readonly>
         <run_address>0x6920</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-190">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x6930</load_address>
         <readonly>true</readonly>
         <run_address>0x6930</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.rodata.gI2C_0ClockConfig</name>
         <load_address>0x693a</load_address>
         <readonly>true</readonly>
         <run_address>0x693a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-197">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0x693c</load_address>
         <readonly>true</readonly>
         <run_address>0x693c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-187">
         <name>.rodata.gPWM_0Config</name>
         <load_address>0x6944</load_address>
         <readonly>true</readonly>
         <run_address>0x6944</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.rodata.gMCAN0ClockConf</name>
         <load_address>0x694c</load_address>
         <readonly>true</readonly>
         <run_address>0x694c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.rodata.gMCAN1ClockConf</name>
         <load_address>0x6950</load_address>
         <readonly>true</readonly>
         <run_address>0x6950</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-186">
         <name>.rodata.gPWM_0ClockConfig</name>
         <load_address>0x6954</load_address>
         <readonly>true</readonly>
         <run_address>0x6954</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-189">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x6957</load_address>
         <readonly>true</readonly>
         <run_address>0x6957</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x695a</load_address>
         <readonly>true</readonly>
         <run_address>0x695a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-214">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f2">
         <name>.data.Working_mode</name>
         <load_address>0x2020054e</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020054e</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-133">
         <name>.data.FET_TEST</name>
         <load_address>0x20200545</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200545</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-91">
         <name>.data.Systick_counter</name>
         <load_address>0x20200518</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200518</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.data.gWAKEINMCU</name>
         <load_address>0x20200553</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200553</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-134">
         <name>.data.gCANIDSet</name>
         <load_address>0x20200550</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200550</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-90">
         <name>.data.gTIMER0</name>
         <load_address>0x20200552</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200552</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-138">
         <name>.data.BOTFULLSCANCycle</name>
         <load_address>0x20200543</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200543</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-139">
         <name>.data.BOTFULLSCANCycle2</name>
         <load_address>0x20200542</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200542</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-136">
         <name>.data.TOPFULLSCANCycle</name>
         <load_address>0x2020054c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020054c</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-137">
         <name>.data.TOPFULLSCANCycle2</name>
         <load_address>0x2020054b</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020054b</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.data.PASSQ_TIME_MIN</name>
         <load_address>0x20200548</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200548</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-144">
         <name>.data.BOTBatteryStatus</name>
         <load_address>0x20200534</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200534</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-154">
         <name>.data.BOTProtectionFlag</name>
         <load_address>0x20200536</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200536</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-147">
         <name>.data.TOPBatteryStatus</name>
         <load_address>0x2020053e</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020053e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-153">
         <name>.data.TOPProtectionFlag</name>
         <load_address>0x20200540</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200540</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.data.TopCellVoltage_raw</name>
         <load_address>0x202004e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202004e0</run_address>
         <size>0x20</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.data.BotCellVoltage_raw</name>
         <load_address>0x202004a0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202004a0</run_address>
         <size>0x20</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.data.Qtime_30min</name>
         <load_address>0x20200368</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200368</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.data.PASSQ_30min</name>
         <load_address>0x202002f0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202002f0</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.data.Cell_fixed_offset_LFP</name>
         <load_address>0x20200420</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200420</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.data.TopCellVoltage_cali</name>
         <load_address>0x20200460</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200460</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.data.BotCellVoltage_cali</name>
         <load_address>0x202003e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202003e0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.data.check_signal_pattern.state</name>
         <load_address>0x20200524</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200524</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.data.check_signal_pattern.last_time</name>
         <load_address>0x20200520</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200520</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.data.check_signal_pattern.last_counter</name>
         <load_address>0x2020051c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020051c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.data.ProtectionsTriggered</name>
         <load_address>0x20200549</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200549</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.data.UV_Fault</name>
         <load_address>0x2020054d</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020054d</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.data.OV_Fault</name>
         <load_address>0x20200547</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200547</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.data.SCD_Fault</name>
         <load_address>0x2020054a</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020054a</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.data.OCD2_Fault</name>
         <load_address>0x20200546</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200546</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1ff">
         <name>.data.Pack_Current</name>
         <load_address>0x2020053a</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020053a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-143">
         <name>.data.Battery_status</name>
         <load_address>0x20200538</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200538</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.data.PASS_Q</name>
         <load_address>0x20200514</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200514</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.data.RX_32Byte</name>
         <load_address>0x202004c0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202004c0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-142">
         <name>.data.RX_data</name>
         <load_address>0x2020053c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020053c</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.data.gCANIntLine1Status</name>
         <load_address>0x2020052c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020052c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.data.gCANServiceInt</name>
         <load_address>0x20200551</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200551</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.data.CAN_Status</name>
         <load_address>0x20200544</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200544</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.data.gCANNodeId</name>
         <load_address>0x20200530</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200530</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-83">
         <name>.data.gCAN1IntLine1Status</name>
         <load_address>0x20200528</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200528</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-84">
         <name>.data.gCAN1ServiceInt</name>
         <load_address>0x2020054f</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020054f</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.data.gTxPacket</name>
         <load_address>0x20200500</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200500</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1f2">
         <name>.data.FACTORYVALUE</name>
         <load_address>0x20200510</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200510</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-159">
         <name>.common:send_success</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002eb</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-135">
         <name>.common:PASSQ_read</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002e8</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-12e">
         <name>.common:gPWM_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-12f">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1be">
         <name>.common:value_SafetyStatusA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002ec</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1c3">
         <name>.common:value_SafetyStatusB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002ed</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1c4">
         <name>.common:value_SafetyStatusC</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002ee</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1e0">
         <name>.common:AccumulatedCharge_Int</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002cc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1e1">
         <name>.common:AccumulatedCharge_Frac</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002c8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-15d">
         <name>.common:AccumulatedCharge_Time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002d0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1e2">
         <name>.common:deadband</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002e9</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-14e">
         <name>.common:gCANTxMsg</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-14d">
         <name>.common:gCANRxFS</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002a0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1bb">
         <name>.common:gCANRxMsg</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200178</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-13f">
         <name>.common:I2C_TARGET_ADDRESS</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002d4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-98">
         <name>.common:gI2cControllerStatus</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002ea</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-99">
         <name>.common:gTxCount</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002e0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-9a">
         <name>.common:gTxLen</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-9c">
         <name>.common:gRxLen</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002dc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-9d">
         <name>.common:gRxCount</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002d8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-9e">
         <name>.common:gRxPacket</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202002b8</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-8c">
         <name>.common:gRS485</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200244</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-205">
         <name>.TrimTable</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200554</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020ff00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-252">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020ff00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x85e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_loc</name>
         <load_address>0x85e</load_address>
         <run_address>0x85e</run_address>
         <size>0x30b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_loc</name>
         <load_address>0xb69</load_address>
         <run_address>0xb69</run_address>
         <size>0xe68c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_loc</name>
         <load_address>0xf1f5</load_address>
         <run_address>0xf1f5</run_address>
         <size>0x6ce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_loc</name>
         <load_address>0xf8c3</load_address>
         <run_address>0xf8c3</run_address>
         <size>0x41b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_loc</name>
         <load_address>0xfcde</load_address>
         <run_address>0xfcde</run_address>
         <size>0x2cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-194">
         <name>.debug_loc</name>
         <load_address>0xffad</load_address>
         <run_address>0xffad</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.debug_loc</name>
         <load_address>0x10074</load_address>
         <run_address>0x10074</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_loc</name>
         <load_address>0x10087</load_address>
         <run_address>0x10087</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_loc</name>
         <load_address>0x103d9</load_address>
         <run_address>0x103d9</run_address>
         <size>0x35ae</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_loc</name>
         <load_address>0x13987</load_address>
         <run_address>0x13987</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_loc</name>
         <load_address>0x153ae</load_address>
         <run_address>0x153ae</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_loc</name>
         <load_address>0x15b6a</load_address>
         <run_address>0x15b6a</run_address>
         <size>0x5d3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_loc</name>
         <load_address>0x1613d</load_address>
         <run_address>0x1613d</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_loc</name>
         <load_address>0x16215</load_address>
         <run_address>0x16215</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_loc</name>
         <load_address>0x16639</load_address>
         <run_address>0x16639</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_loc</name>
         <load_address>0x167a5</load_address>
         <run_address>0x167a5</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_loc</name>
         <load_address>0x16814</load_address>
         <run_address>0x16814</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_loc</name>
         <load_address>0x1697b</load_address>
         <run_address>0x1697b</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_loc</name>
         <load_address>0x169a1</load_address>
         <run_address>0x169a1</run_address>
         <size>0x363</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-20a">
         <name>.debug_loc</name>
         <load_address>0x16d04</load_address>
         <run_address>0x16d04</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x298</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0x275</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_abbrev</name>
         <load_address>0x50d</load_address>
         <run_address>0x50d</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_abbrev</name>
         <load_address>0x57a</load_address>
         <run_address>0x57a</run_address>
         <size>0x1b4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_abbrev</name>
         <load_address>0x72e</load_address>
         <run_address>0x72e</run_address>
         <size>0x2d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0xa03</load_address>
         <run_address>0xa03</run_address>
         <size>0x23e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_abbrev</name>
         <load_address>0xc41</load_address>
         <run_address>0xc41</run_address>
         <size>0x202</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_abbrev</name>
         <load_address>0xe43</load_address>
         <run_address>0xe43</run_address>
         <size>0x15b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1f7">
         <name>.debug_abbrev</name>
         <load_address>0xf9e</load_address>
         <run_address>0xf9e</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.debug_abbrev</name>
         <load_address>0x110f</load_address>
         <run_address>0x110f</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_abbrev</name>
         <load_address>0x1171</load_address>
         <run_address>0x1171</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_abbrev</name>
         <load_address>0x1358</load_address>
         <run_address>0x1358</run_address>
         <size>0x229</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1f5">
         <name>.debug_abbrev</name>
         <load_address>0x1581</load_address>
         <run_address>0x1581</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_abbrev</name>
         <load_address>0x1807</load_address>
         <run_address>0x1807</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-1f3">
         <name>.debug_abbrev</name>
         <load_address>0x1aa2</load_address>
         <run_address>0x1aa2</run_address>
         <size>0x274</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-206">
         <name>.debug_abbrev</name>
         <load_address>0x1d16</load_address>
         <run_address>0x1d16</run_address>
         <size>0x91</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_abbrev</name>
         <load_address>0x1da7</load_address>
         <run_address>0x1da7</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_abbrev</name>
         <load_address>0x1e56</load_address>
         <run_address>0x1e56</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_abbrev</name>
         <load_address>0x1fc6</load_address>
         <run_address>0x1fc6</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_abbrev</name>
         <load_address>0x1fff</load_address>
         <run_address>0x1fff</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_abbrev</name>
         <load_address>0x20c1</load_address>
         <run_address>0x20c1</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_abbrev</name>
         <load_address>0x2131</load_address>
         <run_address>0x2131</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_abbrev</name>
         <load_address>0x21be</load_address>
         <run_address>0x21be</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.debug_abbrev</name>
         <load_address>0x2256</load_address>
         <run_address>0x2256</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-201">
         <name>.debug_abbrev</name>
         <load_address>0x2282</load_address>
         <run_address>0x2282</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_abbrev</name>
         <load_address>0x22a9</load_address>
         <run_address>0x22a9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_abbrev</name>
         <load_address>0x22d0</load_address>
         <run_address>0x22d0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-204">
         <name>.debug_abbrev</name>
         <load_address>0x22f7</load_address>
         <run_address>0x22f7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_abbrev</name>
         <load_address>0x231e</load_address>
         <run_address>0x231e</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-203">
         <name>.debug_abbrev</name>
         <load_address>0x2345</load_address>
         <run_address>0x2345</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_abbrev</name>
         <load_address>0x236c</load_address>
         <run_address>0x236c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-200">
         <name>.debug_abbrev</name>
         <load_address>0x2393</load_address>
         <run_address>0x2393</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_abbrev</name>
         <load_address>0x23ba</load_address>
         <run_address>0x23ba</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_abbrev</name>
         <load_address>0x23e1</load_address>
         <run_address>0x23e1</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_abbrev</name>
         <load_address>0x2408</load_address>
         <run_address>0x2408</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-119"/>
      </object_component>
      <object_component id="oc-1fe">
         <name>.debug_abbrev</name>
         <load_address>0x242d</load_address>
         <run_address>0x242d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_abbrev</name>
         <load_address>0x2454</load_address>
         <run_address>0x2454</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-208">
         <name>.debug_abbrev</name>
         <load_address>0x251c</load_address>
         <run_address>0x251c</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_abbrev</name>
         <load_address>0x2575</load_address>
         <run_address>0x2575</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_abbrev</name>
         <load_address>0x259a</load_address>
         <run_address>0x259a</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-123"/>
      </object_component>
      <object_component id="oc-254">
         <name>.debug_abbrev</name>
         <load_address>0x25bf</load_address>
         <run_address>0x25bf</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2313</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_info</name>
         <load_address>0x2313</load_address>
         <run_address>0x2313</run_address>
         <size>0x5f0c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x821f</load_address>
         <run_address>0x821f</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x829f</load_address>
         <run_address>0x829f</run_address>
         <size>0x142e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_info</name>
         <load_address>0x96cd</load_address>
         <run_address>0x96cd</run_address>
         <size>0xc269</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_info</name>
         <load_address>0x15936</load_address>
         <run_address>0x15936</run_address>
         <size>0x1a62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_info</name>
         <load_address>0x17398</load_address>
         <run_address>0x17398</run_address>
         <size>0xccb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_info</name>
         <load_address>0x18063</load_address>
         <run_address>0x18063</run_address>
         <size>0x1077</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_info</name>
         <load_address>0x190da</load_address>
         <run_address>0x190da</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_info</name>
         <load_address>0x1981f</load_address>
         <run_address>0x1981f</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0x19894</load_address>
         <run_address>0x19894</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_info</name>
         <load_address>0x1a556</load_address>
         <run_address>0x1a556</run_address>
         <size>0x5804</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_info</name>
         <load_address>0x1fd5a</load_address>
         <run_address>0x1fd5a</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_info</name>
         <load_address>0x22ecc</load_address>
         <run_address>0x22ecc</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_info</name>
         <load_address>0x24172</load_address>
         <run_address>0x24172</run_address>
         <size>0x13b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1f0">
         <name>.debug_info</name>
         <load_address>0x2552b</load_address>
         <run_address>0x2552b</run_address>
         <size>0x30d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x25838</load_address>
         <run_address>0x25838</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_info</name>
         <load_address>0x25c5b</load_address>
         <run_address>0x25c5b</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x2639f</load_address>
         <run_address>0x2639f</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x263e5</load_address>
         <run_address>0x263e5</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-25">
         <name>.debug_info</name>
         <load_address>0x26577</load_address>
         <run_address>0x26577</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x2663d</load_address>
         <run_address>0x2663d</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_info</name>
         <load_address>0x267b9</load_address>
         <run_address>0x267b9</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_info</name>
         <load_address>0x268b1</load_address>
         <run_address>0x268b1</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_info</name>
         <load_address>0x268ec</load_address>
         <run_address>0x268ec</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0x26a93</load_address>
         <run_address>0x26a93</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_info</name>
         <load_address>0x26c20</load_address>
         <run_address>0x26c20</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.debug_info</name>
         <load_address>0x26daf</load_address>
         <run_address>0x26daf</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_info</name>
         <load_address>0x26f3c</load_address>
         <run_address>0x26f3c</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.debug_info</name>
         <load_address>0x270c9</load_address>
         <run_address>0x270c9</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_info</name>
         <load_address>0x2725c</load_address>
         <run_address>0x2725c</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_info</name>
         <load_address>0x273f3</load_address>
         <run_address>0x273f3</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_info</name>
         <load_address>0x2758a</load_address>
         <run_address>0x2758a</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_info</name>
         <load_address>0x277a1</load_address>
         <run_address>0x277a1</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_info</name>
         <load_address>0x2793a</load_address>
         <run_address>0x2793a</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-119"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.debug_info</name>
         <load_address>0x27aef</load_address>
         <run_address>0x27aef</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_info</name>
         <load_address>0x27cab</load_address>
         <run_address>0x27cab</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1fb">
         <name>.debug_info</name>
         <load_address>0x27fa4</load_address>
         <run_address>0x27fa4</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_info</name>
         <load_address>0x28029</load_address>
         <run_address>0x28029</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_info</name>
         <load_address>0x28323</load_address>
         <run_address>0x28323</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-123"/>
      </object_component>
      <object_component id="oc-253">
         <name>.debug_info</name>
         <load_address>0x28567</load_address>
         <run_address>0x28567</run_address>
         <size>0xce</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_ranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x90</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_ranges</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0x148</load_address>
         <run_address>0x148</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_ranges</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x380</load_address>
         <run_address>0x380</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_ranges</name>
         <load_address>0x3d8</load_address>
         <run_address>0x3d8</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x5a0</load_address>
         <run_address>0x5a0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_ranges</name>
         <load_address>0x5c0</load_address>
         <run_address>0x5c0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_ranges</name>
         <load_address>0x5d8</load_address>
         <run_address>0x5d8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_ranges</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x4d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_ranges</name>
         <load_address>0xc88</load_address>
         <run_address>0xc88</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_ranges</name>
         <load_address>0xe60</load_address>
         <run_address>0xe60</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_ranges</name>
         <load_address>0x1008</load_address>
         <run_address>0x1008</run_address>
         <size>0x1e8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_ranges</name>
         <load_address>0x11f0</load_address>
         <run_address>0x11f0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_ranges</name>
         <load_address>0x1238</load_address>
         <run_address>0x1238</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_ranges</name>
         <load_address>0x1280</load_address>
         <run_address>0x1280</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_ranges</name>
         <load_address>0x1298</load_address>
         <run_address>0x1298</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_ranges</name>
         <load_address>0x12e8</load_address>
         <run_address>0x12e8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_ranges</name>
         <load_address>0x1300</load_address>
         <run_address>0x1300</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-119"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_ranges</name>
         <load_address>0x1328</load_address>
         <run_address>0x1328</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1fd">
         <name>.debug_ranges</name>
         <load_address>0x1360</load_address>
         <run_address>0x1360</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_ranges</name>
         <load_address>0x1378</load_address>
         <run_address>0x1378</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_ranges</name>
         <load_address>0x13a0</load_address>
         <run_address>0x13a0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-123"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x16b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_str</name>
         <load_address>0x16b5</load_address>
         <run_address>0x16b5</run_address>
         <size>0x49ec</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_str</name>
         <load_address>0x60a1</load_address>
         <run_address>0x60a1</run_address>
         <size>0x185</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_str</name>
         <load_address>0x6226</load_address>
         <run_address>0x6226</run_address>
         <size>0xf51</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_str</name>
         <load_address>0x7177</load_address>
         <run_address>0x7177</run_address>
         <size>0xb76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_str</name>
         <load_address>0x7ced</load_address>
         <run_address>0x7ced</run_address>
         <size>0x12a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_str</name>
         <load_address>0x8f94</load_address>
         <run_address>0x8f94</run_address>
         <size>0xae4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_str</name>
         <load_address>0x9a78</load_address>
         <run_address>0x9a78</run_address>
         <size>0x9d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1f8">
         <name>.debug_str</name>
         <load_address>0xa44d</load_address>
         <run_address>0xa44d</run_address>
         <size>0x631</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.debug_str</name>
         <load_address>0xaa7e</load_address>
         <run_address>0xaa7e</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_str</name>
         <load_address>0xabeb</load_address>
         <run_address>0xabeb</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_str</name>
         <load_address>0xb49a</load_address>
         <run_address>0xb49a</run_address>
         <size>0x1f45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1f6">
         <name>.debug_str</name>
         <load_address>0xd3df</load_address>
         <run_address>0xd3df</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_str</name>
         <load_address>0xf1ab</load_address>
         <run_address>0xf1ab</run_address>
         <size>0xce3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-1f4">
         <name>.debug_str</name>
         <load_address>0xfe8e</load_address>
         <run_address>0xfe8e</run_address>
         <size>0x12cd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-207">
         <name>.debug_str</name>
         <load_address>0x1115b</load_address>
         <run_address>0x1115b</run_address>
         <size>0x37d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_str</name>
         <load_address>0x114d8</load_address>
         <run_address>0x114d8</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.debug_str</name>
         <load_address>0x116fd</load_address>
         <run_address>0x116fd</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_str</name>
         <load_address>0x11a2c</load_address>
         <run_address>0x11a2c</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_str</name>
         <load_address>0x11b21</load_address>
         <run_address>0x11b21</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_str</name>
         <load_address>0x11cbc</load_address>
         <run_address>0x11cbc</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_str</name>
         <load_address>0x11e24</load_address>
         <run_address>0x11e24</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_str</name>
         <load_address>0x11ff9</load_address>
         <run_address>0x11ff9</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.debug_str</name>
         <load_address>0x12141</load_address>
         <run_address>0x12141</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_str</name>
         <load_address>0x1222a</load_address>
         <run_address>0x1222a</run_address>
         <size>0x276</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-209">
         <name>.debug_str</name>
         <load_address>0x124a0</load_address>
         <run_address>0x124a0</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x228</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_frame</name>
         <load_address>0x228</load_address>
         <run_address>0x228</run_address>
         <size>0x18c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x3b4</load_address>
         <run_address>0x3b4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_frame</name>
         <load_address>0x3e4</load_address>
         <run_address>0x3e4</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_frame</name>
         <load_address>0x42c</load_address>
         <run_address>0x42c</run_address>
         <size>0x370</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_frame</name>
         <load_address>0x79c</load_address>
         <run_address>0x79c</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_frame</name>
         <load_address>0x8bc</load_address>
         <run_address>0x8bc</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_frame</name>
         <load_address>0x948</load_address>
         <run_address>0x948</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-195">
         <name>.debug_frame</name>
         <load_address>0x9b4</load_address>
         <run_address>0x9b4</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_frame</name>
         <load_address>0xa00</load_address>
         <run_address>0xa00</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0xa20</load_address>
         <run_address>0xa20</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_frame</name>
         <load_address>0xb4c</load_address>
         <run_address>0xb4c</run_address>
         <size>0x6b0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_frame</name>
         <load_address>0x11fc</load_address>
         <run_address>0x11fc</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_frame</name>
         <load_address>0x1604</load_address>
         <run_address>0x1604</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_frame</name>
         <load_address>0x17bc</load_address>
         <run_address>0x17bc</run_address>
         <size>0x160</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1f1">
         <name>.debug_frame</name>
         <load_address>0x191c</load_address>
         <run_address>0x191c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_frame</name>
         <load_address>0x193c</load_address>
         <run_address>0x193c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_frame</name>
         <load_address>0x19cc</load_address>
         <run_address>0x19cc</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0x1acc</load_address>
         <run_address>0x1acc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_frame</name>
         <load_address>0x1aec</load_address>
         <run_address>0x1aec</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x1b24</load_address>
         <run_address>0x1b24</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x1b4c</load_address>
         <run_address>0x1b4c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_frame</name>
         <load_address>0x1b7c</load_address>
         <run_address>0x1b7c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_frame</name>
         <load_address>0x1bac</load_address>
         <run_address>0x1bac</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_frame</name>
         <load_address>0x1bcc</load_address>
         <run_address>0x1bcc</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1fc">
         <name>.debug_frame</name>
         <load_address>0x1c38</load_address>
         <run_address>0x1c38</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_line</name>
         <load_address>0xcd7</load_address>
         <run_address>0xcd7</run_address>
         <size>0xa6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-33">
         <name>.debug_line</name>
         <load_address>0x1742</load_address>
         <run_address>0x1742</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_line</name>
         <load_address>0x17fa</load_address>
         <run_address>0x17fa</run_address>
         <size>0x433</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_line</name>
         <load_address>0x1c2d</load_address>
         <run_address>0x1c2d</run_address>
         <size>0x33ce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0x4ffb</load_address>
         <run_address>0x4ffb</run_address>
         <size>0x76b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x5766</load_address>
         <run_address>0x5766</run_address>
         <size>0x6d2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_line</name>
         <load_address>0x5e38</load_address>
         <run_address>0x5e38</run_address>
         <size>0x4f7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_line</name>
         <load_address>0x632f</load_address>
         <run_address>0x632f</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_line</name>
         <load_address>0x65ae</load_address>
         <run_address>0x65ae</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x6726</load_address>
         <run_address>0x6726</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x6da8</load_address>
         <run_address>0x6da8</run_address>
         <size>0x24b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_line</name>
         <load_address>0x9261</load_address>
         <run_address>0x9261</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0xa9cf</load_address>
         <run_address>0xa9cf</run_address>
         <size>0xa17</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_line</name>
         <load_address>0xb3e6</load_address>
         <run_address>0xb3e6</run_address>
         <size>0xa93</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1ef">
         <name>.debug_line</name>
         <load_address>0xbe79</load_address>
         <run_address>0xbe79</run_address>
         <size>0x29d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0xc116</load_address>
         <run_address>0xc116</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_line</name>
         <load_address>0xc2f2</load_address>
         <run_address>0xc2f2</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0xc80c</load_address>
         <run_address>0xc80c</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0xc84a</load_address>
         <run_address>0xc84a</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-24">
         <name>.debug_line</name>
         <load_address>0xc948</load_address>
         <run_address>0xc948</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-20">
         <name>.debug_line</name>
         <load_address>0xca08</load_address>
         <run_address>0xca08</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_line</name>
         <load_address>0xcbd0</load_address>
         <run_address>0xcbd0</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_line</name>
         <load_address>0xcc37</load_address>
         <run_address>0xcc37</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_line</name>
         <load_address>0xcc78</load_address>
         <run_address>0xcc78</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_line</name>
         <load_address>0xcd7f</load_address>
         <run_address>0xcd7f</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_line</name>
         <load_address>0xce8b</load_address>
         <run_address>0xce8b</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.debug_line</name>
         <load_address>0xcf44</load_address>
         <run_address>0xcf44</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_line</name>
         <load_address>0xd024</load_address>
         <run_address>0xd024</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.debug_line</name>
         <load_address>0xd146</load_address>
         <run_address>0xd146</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_line</name>
         <load_address>0xd202</load_address>
         <run_address>0xd202</run_address>
         <size>0xb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.debug_line</name>
         <load_address>0xd2b4</load_address>
         <run_address>0xd2b4</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_line</name>
         <load_address>0xd368</load_address>
         <run_address>0xd368</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_line</name>
         <load_address>0xd42f</load_address>
         <run_address>0xd42f</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_line</name>
         <load_address>0xd4d3</load_address>
         <run_address>0xd4d3</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-119"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_line</name>
         <load_address>0xd58d</load_address>
         <run_address>0xd58d</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_line</name>
         <load_address>0xd64f</load_address>
         <run_address>0xd64f</run_address>
         <size>0x2ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1fa">
         <name>.debug_line</name>
         <load_address>0xd93e</load_address>
         <run_address>0xd93e</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0xd9f3</load_address>
         <run_address>0xd9f3</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_line</name>
         <load_address>0xda93</load_address>
         <run_address>0xda93</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-123"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1d8">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-119"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.debug_aranges</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_aranges</name>
         <load_address>0x188</load_address>
         <run_address>0x188</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_aranges</name>
         <load_address>0x1b0</load_address>
         <run_address>0x1b0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-123"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x65a0</size>
         <contents>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-1d5"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-1d4"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-1ee"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-1f9"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x6960</load_address>
         <run_address>0x6960</run_address>
         <size>0xa8</size>
         <contents>
            <object_component_ref idref="oc-24e"/>
            <object_component_ref idref="oc-24c"/>
            <object_component_ref idref="oc-250"/>
            <object_component_ref idref="oc-24f"/>
            <object_component_ref idref="oc-24d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x6660</load_address>
         <run_address>0x6660</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-202"/>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-18f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-214"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202002f0</run_address>
         <size>0x264</size>
         <contents>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-1ff"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-1f2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x2ef</size>
         <contents>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.TrimTable</name>
         <run_address>0x20200554</run_address>
         <size>0x44</size>
         <contents>
            <object_component_ref idref="oc-205"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x2020ff00</run_address>
         <size>0x100</size>
         <contents>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-252"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15" display="no" color="cyan">
         <name>.DataBank</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20b" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20c" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20d" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20e" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20f" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-210" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-212" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-230" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x16d24</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-20a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-232" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x25ce</size>
         <contents>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-1f7"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-1f5"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-1f3"/>
            <object_component_ref idref="oc-206"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-201"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-204"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-203"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-200"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-1fe"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-208"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-254"/>
         </contents>
      </logical_group>
      <logical_group id="lg-234" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x28635</size>
         <contents>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-1f0"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-25"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-1fb"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-253"/>
         </contents>
      </logical_group>
      <logical_group id="lg-236" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13c8</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-1fd"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-11d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-238" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x12633</size>
         <contents>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-1f8"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-1f6"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-1f4"/>
            <object_component_ref idref="oc-207"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-209"/>
         </contents>
      </logical_group>
      <logical_group id="lg-23a" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c68</size>
         <contents>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-1f1"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-1fc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-23c" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdb13</size>
         <contents>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-1ef"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-24"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-1fa"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-11e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-246" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d8</size>
         <contents>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-120"/>
         </contents>
      </logical_group>
      <logical_group id="lg-251" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-25c" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6a08</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-25d" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x598</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
      <load_segment id="lg-25e" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x2020ff00</run_address>
         <size>0x100</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-12"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x80000</length>
         <used_space>0x6a08</used_space>
         <unused_space>0x795f8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x65a0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x6660</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x6960</start_address>
               <size>0xa8</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x6a08</start_address>
               <size>0x795f8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM_BANK0</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x10000</length>
         <used_space>0x697</used_space>
         <unused_space>0xf969</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-210"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-212"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x2ef</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202002ef</start_address>
               <size>0x1</size>
            </available_space>
            <allocated_space>
               <start_address>0x202002f0</start_address>
               <size>0x264</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200554</start_address>
               <size>0x44</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200598</start_address>
               <size>0xf968</size>
            </available_space>
            <allocated_space>
               <start_address>0x2020ff00</start_address>
               <size>0x100</size>
               <logical_group_ref idref="lg-12"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM_BANK1</name>
         <page_id>0x0</page_id>
         <origin>0x20210000</origin>
         <length>0x10000</length>
         <used_space>0x0</used_space>
         <unused_space>0x10000</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>DATA</name>
         <page_id>0x0</page_id>
         <origin>0x41d00000</origin>
         <length>0x4000</length>
         <used_space>0x0</used_space>
         <unused_space>0x4000</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x6960</load_address>
            <load_size>0x6f</load_size>
            <run_address>0x202002f0</run_address>
            <run_size>0x264</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.TrimTable</name>
            <load_address>0x69dc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200554</run_address>
            <run_size>0x44</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x69e4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x2ef</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x69ec</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x6a04</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x6a04</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x69d0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x69dc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x100</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20210000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5f">
         <name>BMU_Normal_Mode</name>
         <value>0x3f9d</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-60">
         <name>BatteryDataUpdate_32s</name>
         <value>0x3355</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-61">
         <name>BOTBatteryStatus</name>
         <value>0x20200534</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-62">
         <name>Working_mode</name>
         <value>0x2020054e</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-63">
         <name>TOPProtectionFlag</name>
         <value>0x20200540</value>
         <object_component_ref idref="oc-153"/>
      </symbol>
      <symbol id="sm-64">
         <name>BOTProtectionFlag</name>
         <value>0x20200536</value>
         <object_component_ref idref="oc-154"/>
      </symbol>
      <symbol id="sm-65">
         <name>Systick_counter</name>
         <value>0x20200518</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-66">
         <name>send_success</name>
         <value>0x202002eb</value>
      </symbol>
      <symbol id="sm-67">
         <name>TopCellVoltage_raw</name>
         <value>0x202004e0</value>
         <object_component_ref idref="oc-15a"/>
      </symbol>
      <symbol id="sm-68">
         <name>PASSQ_read</name>
         <value>0x202002e8</value>
      </symbol>
      <symbol id="sm-69">
         <name>PASSQ_TIME_MIN</name>
         <value>0x20200548</value>
         <object_component_ref idref="oc-13a"/>
      </symbol>
      <symbol id="sm-6a">
         <name>PASSQ_30min</name>
         <value>0x202002f0</value>
         <object_component_ref idref="oc-15c"/>
      </symbol>
      <symbol id="sm-6b">
         <name>Qtime_30min</name>
         <value>0x20200368</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-6c">
         <name>gTIMER0</name>
         <value>0x20200552</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-6d">
         <name>BotCellVoltage_raw</name>
         <value>0x202004a0</value>
         <object_component_ref idref="oc-1d0"/>
      </symbol>
      <symbol id="sm-6e">
         <name>Cell_fixed_offset_LFP</name>
         <value>0x20200420</value>
         <object_component_ref idref="oc-1d1"/>
      </symbol>
      <symbol id="sm-6f">
         <name>BotCellVoltage_cali</name>
         <value>0x202003e0</value>
         <object_component_ref idref="oc-1d2"/>
      </symbol>
      <symbol id="sm-70">
         <name>TopCellVoltage_cali</name>
         <value>0x20200460</value>
         <object_component_ref idref="oc-1d3"/>
      </symbol>
      <symbol id="sm-71">
         <name>BMU_Standby_Mode</name>
         <value>0x50c1</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-72">
         <name>TOPBatteryStatus</name>
         <value>0x2020053e</value>
         <object_component_ref idref="oc-147"/>
      </symbol>
      <symbol id="sm-73">
         <name>BMU_Shutdown_Mode</name>
         <value>0x57f1</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-74">
         <name>BMU_Ship_Mode</name>
         <value>0x622d</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-75">
         <name>TOPFULLSCANCycle</name>
         <value>0x2020054c</value>
         <object_component_ref idref="oc-136"/>
      </symbol>
      <symbol id="sm-76">
         <name>BOTFULLSCANCycle</name>
         <value>0x20200543</value>
         <object_component_ref idref="oc-138"/>
      </symbol>
      <symbol id="sm-77">
         <name>FET_TEST</name>
         <value>0x20200545</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-78">
         <name>gWAKEINMCU</name>
         <value>0x20200553</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-79">
         <name>check_signal_pattern</name>
         <value>0x4609</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-7a">
         <name>CAN_ID_Init_on_Startup</name>
         <value>0x3921</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-7b">
         <name>gCANIDSet</name>
         <value>0x20200550</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-7c">
         <name>Variables_Init</name>
         <value>0x5bf1</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TOPFULLSCANCycle2</name>
         <value>0x2020054b</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-7e">
         <name>BOTFULLSCANCycle2</name>
         <value>0x20200542</value>
         <object_component_ref idref="oc-139"/>
      </symbol>
      <symbol id="sm-7f">
         <name>Gpio_Init</name>
         <value>0x5fb5</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-d2">
         <name>SYSCFG_DL_init</name>
         <value>0x5ff9</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-d3">
         <name>SYSCFG_DL_initPower</name>
         <value>0x56e1</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-d4">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x3d11</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-d5">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x5975</value>
         <object_component_ref idref="oc-125"/>
      </symbol>
      <symbol id="sm-d6">
         <name>SYSCFG_DL_PWM_0_init</name>
         <value>0x553d</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-d7">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x5e0d</value>
         <object_component_ref idref="oc-127"/>
      </symbol>
      <symbol id="sm-d8">
         <name>SYSCFG_DL_I2C_0_init</name>
         <value>0x5d61</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-d9">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x5411</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-da">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x5d09</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-db">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x5caf</value>
         <object_component_ref idref="oc-12b"/>
      </symbol>
      <symbol id="sm-dc">
         <name>SYSCFG_DL_MCAN0_init</name>
         <value>0x40c1</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-dd">
         <name>SYSCFG_DL_MCAN1_init</name>
         <value>0x41dd</value>
         <object_component_ref idref="oc-12d"/>
      </symbol>
      <symbol id="sm-de">
         <name>gPWM_0Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-df">
         <name>gTIMER_0Backup</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-ea">
         <name>Default_Handler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-eb">
         <name>Reset_Handler</name>
         <value>0x6657</value>
         <object_component_ref idref="oc-31"/>
      </symbol>
      <symbol id="sm-ec">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-ed">
         <name>NMI_Handler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-ee">
         <name>HardFault_Handler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-ef">
         <name>SVC_Handler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f0">
         <name>PendSV_Handler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f1">
         <name>SysTick_Handler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f2">
         <name>GROUP0_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f3">
         <name>TIMG8_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f4">
         <name>UART3_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f5">
         <name>ADC0_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f6">
         <name>ADC1_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f7">
         <name>DAC0_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f8">
         <name>TIMG9_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-f9">
         <name>SPI0_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-fa">
         <name>SPI1_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-fb">
         <name>SPI2_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-fc">
         <name>UART1_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-fd">
         <name>UART4_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-fe">
         <name>TIMG0_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-ff">
         <name>TIMG6_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-100">
         <name>TIMA1_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-101">
         <name>TIMG7_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-102">
         <name>TIMG12_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-103">
         <name>TIMG14_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-104">
         <name>UART5_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-105">
         <name>I2C1_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-106">
         <name>I2C2_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-107">
         <name>UART7_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-108">
         <name>AESADV_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-109">
         <name>UART6_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-10a">
         <name>LFSS_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-10b">
         <name>DMA_IRQHandler</name>
         <value>0x540f</value>
         <object_component_ref idref="oc-35"/>
      </symbol>
      <symbol id="sm-11d">
         <name>main</name>
         <value>0x5875</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-11e">
         <name>TIMA0_IRQHandler</name>
         <value>0x6305</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-11f">
         <name>GROUP1_IRQHandler</name>
         <value>0x6339</value>
         <object_component_ref idref="oc-36"/>
      </symbol>
      <symbol id="sm-153">
         <name>delayUS</name>
         <value>0x5769</value>
         <object_component_ref idref="oc-132"/>
      </symbol>
      <symbol id="sm-154">
         <name>CRC8</name>
         <value>0x52d1</value>
         <object_component_ref idref="oc-1d4"/>
      </symbol>
      <symbol id="sm-155">
         <name>RX_data</name>
         <value>0x2020053c</value>
         <object_component_ref idref="oc-142"/>
      </symbol>
      <symbol id="sm-156">
         <name>CommandSubcommands</name>
         <value>0x6369</value>
         <object_component_ref idref="oc-152"/>
      </symbol>
      <symbol id="sm-157">
         <name>RX_32Byte</name>
         <value>0x202004c0</value>
         <object_component_ref idref="oc-1df"/>
      </symbol>
      <symbol id="sm-158">
         <name>BQ769x2_ReadSafetyStatus</name>
         <value>0x54a9</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-159">
         <name>value_SafetyStatusA</name>
         <value>0x202002ec</value>
      </symbol>
      <symbol id="sm-15a">
         <name>UV_Fault</name>
         <value>0x2020054d</value>
         <object_component_ref idref="oc-1bf"/>
      </symbol>
      <symbol id="sm-15b">
         <name>OV_Fault</name>
         <value>0x20200547</value>
         <object_component_ref idref="oc-1c0"/>
      </symbol>
      <symbol id="sm-15c">
         <name>SCD_Fault</name>
         <value>0x2020054a</value>
         <object_component_ref idref="oc-1c1"/>
      </symbol>
      <symbol id="sm-15d">
         <name>OCD2_Fault</name>
         <value>0x20200546</value>
         <object_component_ref idref="oc-1c2"/>
      </symbol>
      <symbol id="sm-15e">
         <name>value_SafetyStatusB</name>
         <value>0x202002ed</value>
      </symbol>
      <symbol id="sm-15f">
         <name>value_SafetyStatusC</name>
         <value>0x202002ee</value>
      </symbol>
      <symbol id="sm-160">
         <name>ProtectionsTriggered</name>
         <value>0x20200549</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-161">
         <name>BQ769x2_ReadBatteryStatus</name>
         <value>0x63c1</value>
         <object_component_ref idref="oc-150"/>
      </symbol>
      <symbol id="sm-162">
         <name>Battery_status</name>
         <value>0x20200538</value>
         <object_component_ref idref="oc-143"/>
      </symbol>
      <symbol id="sm-163">
         <name>BQ769x2_ReadAllCellVoltage</name>
         <value>0x5a5d</value>
         <object_component_ref idref="oc-1c7"/>
      </symbol>
      <symbol id="sm-164">
         <name>BQ769x2_ReadCurrent</name>
         <value>0x5e59</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-165">
         <name>Pack_Current</name>
         <value>0x2020053a</value>
         <object_component_ref idref="oc-1ff"/>
      </symbol>
      <symbol id="sm-166">
         <name>BQ769x2_ReadPassQ</name>
         <value>0x48f9</value>
         <object_component_ref idref="oc-158"/>
      </symbol>
      <symbol id="sm-167">
         <name>AccumulatedCharge_Int</name>
         <value>0x202002cc</value>
      </symbol>
      <symbol id="sm-168">
         <name>AccumulatedCharge_Frac</name>
         <value>0x202002c8</value>
      </symbol>
      <symbol id="sm-169">
         <name>AccumulatedCharge_Time</name>
         <value>0x202002d0</value>
      </symbol>
      <symbol id="sm-16a">
         <name>PASS_Q</name>
         <value>0x20200514</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-16b">
         <name>deadband</name>
         <value>0x202002e9</value>
      </symbol>
      <symbol id="sm-16c">
         <name>BQ769x2_BOT_Init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-16d">
         <name>BQ769x2_TOP_Init</name>
         <value>0x1af1</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-187">
         <name>CANTxMsgSendParamInitDefault</name>
         <value>0x64c9</value>
         <object_component_ref idref="oc-14a"/>
      </symbol>
      <symbol id="sm-188">
         <name>gCANTxMsg</name>
         <value>0x202001e0</value>
      </symbol>
      <symbol id="sm-189">
         <name>CANTxMsgSendPoll</name>
         <value>0x3a81</value>
         <object_component_ref idref="oc-1d5"/>
      </symbol>
      <symbol id="sm-18a">
         <name>gCANServiceInt</name>
         <value>0x20200551</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-18b">
         <name>gCANIntLine1Status</name>
         <value>0x2020052c</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-18c">
         <name>CANDLC_Coding</name>
         <value>0x6660</value>
         <object_component_ref idref="oc-202"/>
      </symbol>
      <symbol id="sm-18d">
         <name>CAN_Write</name>
         <value>0x3bc9</value>
         <object_component_ref idref="oc-157"/>
      </symbol>
      <symbol id="sm-18e">
         <name>CAN_Status</name>
         <value>0x20200544</value>
         <object_component_ref idref="oc-1d6"/>
      </symbol>
      <symbol id="sm-18f">
         <name>CANprocessCANRxMsg</name>
         <value>0x4ff5</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-190">
         <name>gCANRxFS</name>
         <value>0x202002a0</value>
      </symbol>
      <symbol id="sm-191">
         <name>gCANRxMsg</name>
         <value>0x20200178</value>
      </symbol>
      <symbol id="sm-192">
         <name>gCANNodeId</name>
         <value>0x20200530</value>
         <object_component_ref idref="oc-14f"/>
      </symbol>
      <symbol id="sm-193">
         <name>CANFD0_IRQHandler</name>
         <value>0x603d</value>
         <object_component_ref idref="oc-3b"/>
      </symbol>
      <symbol id="sm-194">
         <name>gCAN1ServiceInt</name>
         <value>0x2020054f</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-195">
         <name>gCAN1IntLine1Status</name>
         <value>0x20200528</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-196">
         <name>CANFD1_IRQHandler</name>
         <value>0x5ee9</value>
         <object_component_ref idref="oc-40"/>
      </symbol>
      <symbol id="sm-1a9">
         <name>I2C_WriteReg</name>
         <value>0x5179</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>I2C_TARGET_ADDRESS</name>
         <value>0x202002d4</value>
      </symbol>
      <symbol id="sm-1ab">
         <name>I2C_ReadReg</name>
         <value>0x3781</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-1ac">
         <name>I2C0_IRQHandler</name>
         <value>0x4f21</value>
         <object_component_ref idref="oc-47"/>
      </symbol>
      <symbol id="sm-1ad">
         <name>gI2cControllerStatus</name>
         <value>0x202002ea</value>
      </symbol>
      <symbol id="sm-1ae">
         <name>gTxCount</name>
         <value>0x202002e0</value>
      </symbol>
      <symbol id="sm-1af">
         <name>gTxLen</name>
         <value>0x202002e4</value>
      </symbol>
      <symbol id="sm-1b0">
         <name>gTxPacket</name>
         <value>0x20200500</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>gRxLen</name>
         <value>0x202002dc</value>
      </symbol>
      <symbol id="sm-1b2">
         <name>gRxCount</name>
         <value>0x202002d8</value>
      </symbol>
      <symbol id="sm-1b3">
         <name>gRxPacket</name>
         <value>0x202002b8</value>
      </symbol>
      <symbol id="sm-1c1">
         <name>RS485_Send</name>
         <value>0x3e59</value>
         <object_component_ref idref="oc-156"/>
      </symbol>
      <symbol id="sm-1c2">
         <name>gRS485</name>
         <value>0x20200244</value>
      </symbol>
      <symbol id="sm-1c3">
         <name>UART0_IRQHandler</name>
         <value>0x5225</value>
         <object_component_ref idref="oc-41"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c5">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c6">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c7">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c8">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c9">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1ca">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cb">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cc">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d7">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x607d</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-1e0">
         <name>DL_Common_delayCycles</name>
         <value>0x6639</value>
         <object_component_ref idref="oc-13b"/>
      </symbol>
      <symbol id="sm-1ef">
         <name>DL_I2C_setClockConfig</name>
         <value>0x6439</value>
         <object_component_ref idref="oc-18b"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x5c51</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>DL_I2C_flushControllerTXFIFO</name>
         <value>0x613d</value>
         <object_component_ref idref="oc-1b3"/>
      </symbol>
      <symbol id="sm-239">
         <name>DL_MCAN_setClockConfig</name>
         <value>0x6461</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-23a">
         <name>DL_MCAN_isMemInitDone</name>
         <value>0x6601</value>
         <object_component_ref idref="oc-19a"/>
      </symbol>
      <symbol id="sm-23b">
         <name>DL_MCAN_setOpMode</name>
         <value>0x6571</value>
         <object_component_ref idref="oc-19b"/>
      </symbol>
      <symbol id="sm-23c">
         <name>DL_MCAN_getOpMode</name>
         <value>0x65f1</value>
         <object_component_ref idref="oc-148"/>
      </symbol>
      <symbol id="sm-23d">
         <name>DL_MCAN_init</name>
         <value>0x4c95</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-23e">
         <name>DL_MCAN_config</name>
         <value>0x4acd</value>
         <object_component_ref idref="oc-19d"/>
      </symbol>
      <symbol id="sm-23f">
         <name>DL_MCAN_setBitTime</name>
         <value>0x4d71</value>
         <object_component_ref idref="oc-19e"/>
      </symbol>
      <symbol id="sm-240">
         <name>DL_MCAN_msgRAMConfig</name>
         <value>0x356d</value>
         <object_component_ref idref="oc-19f"/>
      </symbol>
      <symbol id="sm-241">
         <name>DL_MCAN_setExtIDAndMask</name>
         <value>0x62d1</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-242">
         <name>DL_MCAN_writeMsgRam</name>
         <value>0x4805</value>
         <object_component_ref idref="oc-14b"/>
      </symbol>
      <symbol id="sm-243">
         <name>DL_MCAN_TXBufAddReq</name>
         <value>0x629d</value>
         <object_component_ref idref="oc-14c"/>
      </symbol>
      <symbol id="sm-244">
         <name>DL_MCAN_readMsgRam</name>
         <value>0x42ed</value>
         <object_component_ref idref="oc-1b9"/>
      </symbol>
      <symbol id="sm-245">
         <name>DL_MCAN_addStdMsgIDFilter</name>
         <value>0x6395</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-246">
         <name>DL_MCAN_enableIntr</name>
         <value>0x64e5</value>
         <object_component_ref idref="oc-1a2"/>
      </symbol>
      <symbol id="sm-247">
         <name>DL_MCAN_selectIntrLine</name>
         <value>0x651d</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-248">
         <name>DL_MCAN_enableIntrLine</name>
         <value>0x6501</value>
         <object_component_ref idref="oc-1a4"/>
      </symbol>
      <symbol id="sm-249">
         <name>DL_MCAN_getIntrStatus</name>
         <value>0x6621</value>
         <object_component_ref idref="oc-77"/>
      </symbol>
      <symbol id="sm-24a">
         <name>DL_MCAN_clearIntrStatus</name>
         <value>0x65b9</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-24b">
         <name>DL_MCAN_getRxFIFOStatus</name>
         <value>0x6265</value>
         <object_component_ref idref="oc-1b8"/>
      </symbol>
      <symbol id="sm-24c">
         <name>DL_MCAN_writeRxFIFOAck</name>
         <value>0x60bd</value>
         <object_component_ref idref="oc-1ba"/>
      </symbol>
      <symbol id="sm-24d">
         <name>DL_MCAN_getRevisionId</name>
         <value>0x5f2d</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-269">
         <name>DL_Timer_setClockConfig</name>
         <value>0x6555</value>
         <object_component_ref idref="oc-17c"/>
      </symbol>
      <symbol id="sm-26a">
         <name>DL_Timer_initTimerMode</name>
         <value>0x49e5</value>
         <object_component_ref idref="oc-188"/>
      </symbol>
      <symbol id="sm-26b">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x6611</value>
         <object_component_ref idref="oc-185"/>
      </symbol>
      <symbol id="sm-26c">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x6539</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-26d">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x6589</value>
         <object_component_ref idref="oc-183"/>
      </symbol>
      <symbol id="sm-26e">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x4505</value>
         <object_component_ref idref="oc-182"/>
      </symbol>
      <symbol id="sm-281">
         <name>DL_UART_init</name>
         <value>0x5ea1</value>
         <object_component_ref idref="oc-18e"/>
      </symbol>
      <symbol id="sm-282">
         <name>DL_UART_setClockConfig</name>
         <value>0x65cd</value>
         <object_component_ref idref="oc-18d"/>
      </symbol>
      <symbol id="sm-283">
         <name>DL_UART_receiveDataBlocking</name>
         <value>0x64a9</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-284">
         <name>DL_UART_fillTXFIFO</name>
         <value>0x5db9</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-295">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x470d</value>
         <object_component_ref idref="oc-179"/>
      </symbol>
      <symbol id="sm-296">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x5f71</value>
         <object_component_ref idref="oc-17a"/>
      </symbol>
      <symbol id="sm-297">
         <name>DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <value>0x5659</value>
         <object_component_ref idref="oc-173"/>
      </symbol>
      <symbol id="sm-2a0">
         <name>DL_FactoryRegion_initTrimTable</name>
         <value>0x5cb1</value>
         <object_component_ref idref="oc-1ee"/>
      </symbol>
      <symbol id="sm-2a1">
         <name>DL_FACTORYREGION_TrimTable</name>
         <value>0x20200554</value>
         <object_component_ref idref="oc-205"/>
      </symbol>
      <symbol id="sm-2a2">
         <name>FACTORYVALUE</name>
         <value>0x20200510</value>
         <object_component_ref idref="oc-1f2"/>
      </symbol>
      <symbol id="sm-2b4">
         <name>_c_int00_noargs</name>
         <value>0x6411</value>
         <object_component_ref idref="oc-68"/>
      </symbol>
      <symbol id="sm-2b5">
         <name>__stack</name>
         <value>0x2020ff00</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2c1">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x61b5</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-2c9">
         <name>_system_pre_init</name>
         <value>0x665b</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>__TI_zero_init_nomemset</name>
         <value>0x65a1</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-2dd">
         <name>__TI_decompress_none</name>
         <value>0x65df</value>
         <object_component_ref idref="oc-22"/>
      </symbol>
      <symbol id="sm-2e8">
         <name>__TI_decompress_lzss</name>
         <value>0x58f9</value>
         <object_component_ref idref="oc-1e"/>
      </symbol>
      <symbol id="sm-2f7">
         <name>abort</name>
         <value>0x664d</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-30b">
         <name>HOSTexit</name>
         <value>0x6653</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-30c">
         <name>C$$EXIT</name>
         <value>0x6652</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-321">
         <name>__aeabi_fadd</name>
         <value>0x4e53</value>
         <object_component_ref idref="oc-1cc"/>
      </symbol>
      <symbol id="sm-322">
         <name>__addsf3</name>
         <value>0x4e53</value>
         <object_component_ref idref="oc-1cc"/>
      </symbol>
      <symbol id="sm-323">
         <name>__aeabi_fsub</name>
         <value>0x4e49</value>
         <object_component_ref idref="oc-1cc"/>
      </symbol>
      <symbol id="sm-324">
         <name>__subsf3</name>
         <value>0x4e49</value>
         <object_component_ref idref="oc-1cc"/>
      </symbol>
      <symbol id="sm-32a">
         <name>__aeabi_dmul</name>
         <value>0x4bb1</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-32b">
         <name>__muldf3</name>
         <value>0x4bb1</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-331">
         <name>__muldsi3</name>
         <value>0x61f1</value>
         <object_component_ref idref="oc-10d"/>
      </symbol>
      <symbol id="sm-337">
         <name>__aeabi_fmul</name>
         <value>0x55cd</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-338">
         <name>__mulsf3</name>
         <value>0x55cd</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-33e">
         <name>__aeabi_ddiv</name>
         <value>0x43f9</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-33f">
         <name>__divdf3</name>
         <value>0x43f9</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-345">
         <name>__aeabi_i2f</name>
         <value>0x6179</value>
         <object_component_ref idref="oc-1d7"/>
      </symbol>
      <symbol id="sm-346">
         <name>__floatsisf</name>
         <value>0x6179</value>
         <object_component_ref idref="oc-1d7"/>
      </symbol>
      <symbol id="sm-34c">
         <name>__aeabi_ui2d</name>
         <value>0x6485</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-34d">
         <name>__floatunsidf</name>
         <value>0x6485</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-353">
         <name>__aeabi_ui2f</name>
         <value>0x63e9</value>
         <object_component_ref idref="oc-1c8"/>
      </symbol>
      <symbol id="sm-354">
         <name>__floatunsisf</name>
         <value>0x63e9</value>
         <object_component_ref idref="oc-1c8"/>
      </symbol>
      <symbol id="sm-35a">
         <name>__aeabi_dcmpeq</name>
         <value>0x5b2d</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-35b">
         <name>__aeabi_dcmplt</name>
         <value>0x5b41</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-35c">
         <name>__aeabi_dcmple</name>
         <value>0x5b55</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-35d">
         <name>__aeabi_dcmpge</name>
         <value>0x5b69</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-35e">
         <name>__aeabi_dcmpgt</name>
         <value>0x5b7d</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-364">
         <name>__aeabi_memcpy</name>
         <value>0x6645</value>
         <object_component_ref idref="oc-58"/>
      </symbol>
      <symbol id="sm-365">
         <name>__aeabi_memcpy4</name>
         <value>0x6645</value>
         <object_component_ref idref="oc-58"/>
      </symbol>
      <symbol id="sm-366">
         <name>__aeabi_memcpy8</name>
         <value>0x6645</value>
         <object_component_ref idref="oc-58"/>
      </symbol>
      <symbol id="sm-36d">
         <name>__aeabi_memclr</name>
         <value>0x662d</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-36e">
         <name>__aeabi_memclr4</name>
         <value>0x662d</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-36f">
         <name>__aeabi_memclr8</name>
         <value>0x662d</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-375">
         <name>__aeabi_uidiv</name>
         <value>0x60fd</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-376">
         <name>__aeabi_uidivmod</name>
         <value>0x60fd</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-384">
         <name>__ledf2</name>
         <value>0x5ac5</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-385">
         <name>__gedf2</name>
         <value>0x59e9</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-386">
         <name>__cmpdf2</name>
         <value>0x5ac5</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-387">
         <name>__eqdf2</name>
         <value>0x5ac5</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-388">
         <name>__ltdf2</name>
         <value>0x5ac5</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-389">
         <name>__nedf2</name>
         <value>0x5ac5</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-38a">
         <name>__gtdf2</name>
         <value>0x59e9</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-394">
         <name>__aeabi_idiv0</name>
         <value>0x622b</value>
         <object_component_ref idref="oc-1f9"/>
      </symbol>
      <symbol id="sm-3ae">
         <name>memcpy</name>
         <value>0x5375</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-3bd">
         <name>memset</name>
         <value>0x5b8f</value>
         <object_component_ref idref="oc-121"/>
      </symbol>
      <symbol id="sm-3be">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3c1">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3c2">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
