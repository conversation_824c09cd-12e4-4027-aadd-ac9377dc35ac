******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Mon Jun 16 12:34:42 2025

OUTPUT FILE NAME:   <bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: ********


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 ********   ********  00006a08  000795f8  R  X
  SRAM_BANK0            ********   ********  ********  0000f969  RW X
  SRAM_BANK1            ********   ********  ********  ********  RW X
  BCR_CONFIG            41c00000   000000ff  ********  000000ff  R   
  BSL_CONFIG            41c00100   ********  ********  ********  R   
  DATA                  41d00000   ********  ********  ********  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
********    ********    00006a08   00006a08    r-x
  ********    ********    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000065a0   000065a0    r-x .text
  ********    ********    ********   ********    r-- .rodata
  ********    ********    000000a8   000000a8    r-- .cinit
********    ********    ********   ********    rw-
  ********    ********    000002ef   ********    rw- .bss
  202002f0    202002f0    ********   ********    rw- .data
  ********    ********    ********   ********    rw- .TrimTable
2020ff00    2020ff00    ********   ********    rw-
  2020ff00    2020ff00    ********   ********    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    ********    000000c0     
                  ********    000000c0     startup_mspm0g351x_ticlang.o (.intvecs)

.text      0    000000c0    000065a0     
                  000000c0    00001a30     BQ769x2_protocol.o (.text.BQ769x2_BOT_Init)
                  00001af0    00001864     BQ769x2_protocol.o (.text.BQ769x2_TOP_Init)
                  00003354    00000218     App_task.o (.text.BatteryDataUpdate_32s)
                  0000356c    00000214     driverlib.a : dl_mcan.o (.text.DL_MCAN_msgRAMConfig)
                  00003780    000001a0     I2C_communication.o (.text.I2C_ReadReg)
                  00003920    00000160     App_task.o (.text.CAN_ID_Init_on_Startup)
                  00003a80    00000148     CAN_communication.o (.text.CANTxMsgSendPoll)
                  00003bc8    00000148     CAN_communication.o (.text.CAN_Write)
                  00003d10    00000148     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003e58    00000144     UART_communication.o (.text.RS485_Send)
                  00003f9c    00000124     App_task.o (.text.BMU_Normal_Mode)
                  000040c0    0000011c     ti_msp_dl_config.o (.text.SYSCFG_DL_MCAN0_init)
                  000041dc    00000110     ti_msp_dl_config.o (.text.SYSCFG_DL_MCAN1_init)
                  000042ec    0000010c     driverlib.a : dl_mcan.o (.text.DL_MCAN_readMsgRam)
                  000043f8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00004504    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004608    00000104     App_task.o (.text.check_signal_pattern)
                  0000470c    000000f8     driverlib.a : dl_sysctl_mspm0gx51x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004804    000000f4                 : dl_mcan.o (.text.DL_MCAN_writeMsgRam)
                  000048f8    000000ec     BQ769x2_protocol.o (.text.BQ769x2_ReadPassQ)
                  000049e4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00004acc    000000e4                 : dl_mcan.o (.text.DL_MCAN_config)
                  00004bb0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004c94    000000dc     driverlib.a : dl_mcan.o (.text.DL_MCAN_init)
                  00004d70    000000d8                 : dl_mcan.o (.text.DL_MCAN_setBitTime)
                  00004e48    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004f20    000000d4     I2C_communication.o (.text.I2C0_IRQHandler)
                  00004ff4    000000cc     CAN_communication.o (.text.CANprocessCANRxMsg)
                  000050c0    000000b8     App_task.o (.text.BMU_Standby_Mode)
                  00005178    000000ac     I2C_communication.o (.text.I2C_WriteReg)
                  00005224    000000ac     UART_communication.o (.text.UART0_IRQHandler)
                  000052d0    000000a4     BQ769x2_protocol.o (.text.CRC8)
                  00005374    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000540e    00000002     startup_mspm0g351x_ticlang.o (.text.Default_Handler)
                  00005410    00000098     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000054a8    00000094     BQ769x2_protocol.o (.text.BQ769x2_ReadSafetyStatus)
                  0000553c    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  000055cc    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005658    00000088     driverlib.a : dl_sysctl_mspm0gx51x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000056e0    00000088     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005768    00000088     BQ769x2_protocol.o (.text.delayUS)
                  000057f0    00000084     App_task.o (.text.BMU_Shutdown_Mode)
                  00005874    00000084     main.o (.text.main)
                  000058f8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005974    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000059e8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005a5c    00000068     BQ769x2_protocol.o (.text.BQ769x2_ReadAllCellVoltage)
                  00005ac4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005b2c    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005b8e    00000062     libc.a : memset16.S.obj (.text:memset)
                  00005bf0    00000060     App_task.o (.text.Variables_Init)
                  00005c50    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005cae    00000002     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005cb0    00000058     driverlib.a : dl_factoryregion.o (.text.DL_FactoryRegion_initTrimTable)
                  00005d08    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00005d60    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_0_init)
                  00005db8    00000054     driverlib.a : dl_uart.o (.text.DL_UART_fillTXFIFO)
                  00005e0c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00005e58    00000048     BQ769x2_protocol.o (.text.BQ769x2_ReadCurrent)
                  00005ea0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00005ee8    ********     CAN_communication.o (.text.CANFD1_IRQHandler)
                  00005f2c    ********     driverlib.a : dl_mcan.o (.text.DL_MCAN_getRevisionId)
                  00005f70    ********                 : dl_sysctl_mspm0gx51x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00005fb4    ********     App_task.o (.text.Gpio_Init)
                  00005ff8    ********     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000603c    00000040     CAN_communication.o (.text.CANFD0_IRQHandler)
                  0000607c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000060bc    00000040                 : dl_mcan.o (.text.DL_MCAN_writeRxFIFOAck)
                  000060fc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000613c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006178    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000061b4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000061f0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000622a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000622c    00000038     App_task.o (.text.BMU_Ship_Mode)
                  00006264    00000038     driverlib.a : dl_mcan.o (.text.DL_MCAN_getRxFIFOStatus)
                  0000629c    00000034                 : dl_mcan.o (.text.DL_MCAN_TXBufAddReq)
                  000062d0    00000034                 : dl_mcan.o (.text.DL_MCAN_setExtIDAndMask)
                  00006304    00000034     main.o (.text.TIMA0_IRQHandler)
                  00006338    00000030     main.o (.text.GROUP1_IRQHandler)
                  00006368    0000002c     BQ769x2_protocol.o (.text.CommandSubcommands)
                  00006394    0000002c     driverlib.a : dl_mcan.o (.text.DL_MCAN_addStdMsgIDFilter)
                  000063c0    00000028     BQ769x2_protocol.o (.text.BQ769x2_ReadBatteryStatus)
                  000063e8    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006410    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006438    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000645e    00000002     --HOLE-- [fill = 0]
                  00006460    00000024                 : dl_mcan.o (.text.DL_MCAN_setClockConfig)
                  00006484    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000064a8    00000020     driverlib.a : dl_uart.o (.text.DL_UART_receiveDataBlocking)
                  000064c8    0000001c     CAN_communication.o (.text.CANTxMsgSendParamInitDefault)
                  000064e4    0000001c     driverlib.a : dl_mcan.o (.text.DL_MCAN_enableIntr)
                  00006500    0000001c                 : dl_mcan.o (.text.DL_MCAN_enableIntrLine)
                  0000651c    0000001c                 : dl_mcan.o (.text.DL_MCAN_selectIntrLine)
                  00006538    0000001c                 : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00006554    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00006570    00000018                 : dl_mcan.o (.text.DL_MCAN_setOpMode)
                  00006588    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000065a0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000065b6    00000002     --HOLE-- [fill = 0]
                  000065b8    00000014     driverlib.a : dl_mcan.o (.text.DL_MCAN_clearIntrStatus)
                  000065cc    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  000065de    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000065f0    00000010     driverlib.a : dl_mcan.o (.text.DL_MCAN_getOpMode)
                  00006600    00000010                 : dl_mcan.o (.text.DL_MCAN_isMemInitDone)
                  00006610    00000010                 : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00006620    0000000c                 : dl_mcan.o (.text.DL_MCAN_getIntrStatus)
                  0000662c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00006638    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00006642    00000002     --HOLE-- [fill = 0]
                  00006644    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000664c    00000006     libc.a : exit.c.obj (.text:abort)
                  00006652    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00006656    00000004     startup_mspm0g351x_ticlang.o (.text.Reset_Handler)
                  0000665a    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000665e    00000002     --HOLE-- [fill = 0]

.cinit     0    ********    000000a8     
                  ********    0000006f     (.cinit..data.load) [load image, compression = lzss]
                  000069cf    00000001     --HOLE-- [fill = 0]
                  000069d0    0000000c     (__TI_handler_table)
                  000069dc    00000008     (.cinit..TrimTable.load) [load image, compression = zero_init]
                  000069e4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000069ec    00000018     (__TI_cinit_table)
                  00006a04    00000004     --HOLE-- [fill = 0]

.rodata    0    ********    ********     
                  ********    ********     CAN_communication.o (.rodata.CANDLC_Coding)
                  000066e0    00000060     ti_msp_dl_config.o (.rodata.gMCAN0MsgRAMConfigParams)
                  00006740    00000060     ti_msp_dl_config.o (.rodata.gMCAN1MsgRAMConfigParams)
                  000067a0    00000040     driverlib.a : dl_mcan.o (.rodata..L__const.DL_MCAN_getDataSize.dataSize)
                  000067e0    00000034     ti_msp_dl_config.o (.rodata.gMCAN0InitParams)
                  00006814    00000034     ti_msp_dl_config.o (.rodata.gMCAN1InitParams)
                  00006848    0000002c     ti_msp_dl_config.o (.rodata.gMCAN0ConfigParams)
                  00006874    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000689c    00000020     driverlib.a : dl_mcan.o (.rodata.cst32)
                  000068bc    00000020     ti_msp_dl_config.o (.rodata.gMCAN0BitTimes)
                  000068dc    00000020     ti_msp_dl_config.o (.rodata.gMCAN1BitTimes)
                  000068fc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00006910    00000010     ti_msp_dl_config.o (.rodata.gMCAN0StdFiltelem)
                  00006920    00000010     ti_msp_dl_config.o (.rodata.gMCAN1StdFiltelem)
                  00006930    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000693a    00000002     ti_msp_dl_config.o (.rodata.gI2C_0ClockConfig)
                  0000693c    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00006944    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  0000694c    00000004     ti_msp_dl_config.o (.rodata.gMCAN0ClockConf)
                  00006950    00000004     ti_msp_dl_config.o (.rodata.gMCAN1ClockConf)
                  00006954    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00006957    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000695a    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000695c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    ********    ********     UNINITIALIZED

.binit     0    ********    ********     

.bss       0    ********    000002ef     UNINITIALIZED
                  ********    000000bc     (.common:gPWM_0Backup)
                  202000bc    000000bc     (.common:gTIMER_0Backup)
                  20200178    00000068     (.common:gCANRxMsg)
                  202001e0    00000064     (.common:gCANTxMsg)
                  20200244    0000005c     (.common:gRS485)
                  202002a0    00000018     (.common:gCANRxFS)
                  202002b8    00000010     (.common:gRxPacket)
                  202002c8    00000004     (.common:AccumulatedCharge_Frac)
                  202002cc    00000004     (.common:AccumulatedCharge_Int)
                  202002d0    00000004     (.common:AccumulatedCharge_Time)
                  202002d4    00000004     (.common:I2C_TARGET_ADDRESS)
                  202002d8    00000004     (.common:gRxCount)
                  202002dc    00000004     (.common:gRxLen)
                  202002e0    00000004     (.common:gTxCount)
                  202002e4    00000004     (.common:gTxLen)
                  202002e8    00000001     (.common:PASSQ_read)
                  202002e9    00000001     (.common:deadband)
                  202002ea    00000001     (.common:gI2cControllerStatus)
                  202002eb    00000001     (.common:send_success)
                  202002ec    00000001     (.common:value_SafetyStatusA)
                  202002ed    00000001     (.common:value_SafetyStatusB)
                  202002ee    00000001     (.common:value_SafetyStatusC)

.data      0    202002f0    ********     UNINITIALIZED
                  202002f0    00000078     App_task.o (.data.PASSQ_30min)
                  20200368    00000078     App_task.o (.data.Qtime_30min)
                  202003e0    00000040     App_task.o (.data.BotCellVoltage_cali)
                  20200420    00000040     App_task.o (.data.Cell_fixed_offset_LFP)
                  20200460    00000040     App_task.o (.data.TopCellVoltage_cali)
                  202004a0    00000020     App_task.o (.data.BotCellVoltage_raw)
                  202004c0    00000020     BQ769x2_protocol.o (.data.RX_32Byte)
                  202004e0    00000020     App_task.o (.data.TopCellVoltage_raw)
                  20200500    00000010     I2C_communication.o (.data.gTxPacket)
                  20200510    00000004     driverlib.a : dl_factoryregion.o (.data.FACTORYVALUE)
                  20200514    00000004     BQ769x2_protocol.o (.data.PASS_Q)
                  20200518    00000004     App_task.o (.data.Systick_counter)
                  2020051c    00000004     App_task.o (.data.check_signal_pattern.last_counter)
                  20200520    00000004     App_task.o (.data.check_signal_pattern.last_time)
                  20200524    00000004     App_task.o (.data.check_signal_pattern.state)
                  20200528    00000004     CAN_communication.o (.data.gCAN1IntLine1Status)
                  2020052c    00000004     CAN_communication.o (.data.gCANIntLine1Status)
                  20200530    00000004     CAN_communication.o (.data.gCANNodeId)
                  20200534    00000002     App_task.o (.data.BOTBatteryStatus)
                  20200536    00000002     App_task.o (.data.BOTProtectionFlag)
                  20200538    00000002     BQ769x2_protocol.o (.data.Battery_status)
                  2020053a    00000002     BQ769x2_protocol.o (.data.Pack_Current)
                  2020053c    00000002     BQ769x2_protocol.o (.data.RX_data)
                  2020053e    00000002     App_task.o (.data.TOPBatteryStatus)
                  20200540    00000002     App_task.o (.data.TOPProtectionFlag)
                  20200542    00000001     App_task.o (.data.BOTFULLSCANCycle2)
                  20200543    00000001     App_task.o (.data.BOTFULLSCANCycle)
                  20200544    00000001     CAN_communication.o (.data.CAN_Status)
                  20200545    00000001     App_task.o (.data.FET_TEST)
                  20200546    00000001     BQ769x2_protocol.o (.data.OCD2_Fault)
                  20200547    00000001     BQ769x2_protocol.o (.data.OV_Fault)
                  20200548    00000001     App_task.o (.data.PASSQ_TIME_MIN)
                  20200549    00000001     BQ769x2_protocol.o (.data.ProtectionsTriggered)
                  2020054a    00000001     BQ769x2_protocol.o (.data.SCD_Fault)
                  2020054b    00000001     App_task.o (.data.TOPFULLSCANCycle2)
                  2020054c    00000001     App_task.o (.data.TOPFULLSCANCycle)
                  2020054d    00000001     BQ769x2_protocol.o (.data.UV_Fault)
                  2020054e    00000001     App_task.o (.data.Working_mode)
                  2020054f    00000001     CAN_communication.o (.data.gCAN1ServiceInt)
                  20200550    00000001     App_task.o (.data.gCANIDSet)
                  20200551    00000001     CAN_communication.o (.data.gCANServiceInt)
                  20200552    00000001     App_task.o (.data.gTIMER0)
                  20200553    00000001     App_task.o (.data.gWAKEINMCU)

.TrimTable 
*          0    ********    ********     UNINITIALIZED
                  ********    ********     driverlib.a : dl_factoryregion.o (.TrimTable)

.stack     0    2020ff00    ********     UNINITIALIZED
                  2020ff00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  2020ff04    000000fc     --HOLE--

__llvm_prf_cnts 
*          0    ********    ********     UNINITIALIZED

__llvm_prf_bits 
*          0    ********    ********     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             1754    540       376    
       App_task.o                     1976    0         532    
       main.o                         232     0         0      
       startup_mspm0g351x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3968    732       908    
                                                               
    .\BQ769x2_Configs\
       BQ769x2_protocol.o             13892   0         63     
    +--+------------------------------+-------+---------+---------+
       Total:                         13892   0         63     
                                                               
    .\Communications\
       CAN_communication.o            1020    128       243    
       I2C_communication.o            800     0         53     
       UART_communication.o           496     0         92     
    +--+------------------------------+-------+---------+---------+
       Total:                         2316    128       388    
                                                               
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0gx51x/driverlib.a
       dl_mcan.o                      2252    96        0      
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0gx51x.o         452     0         0      
       dl_uart.o                      206     0         0      
       dl_i2c.o                       192     0         0      
       dl_factoryregion.o             88      0         72     
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3852    96        72     
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         526     0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       aeabi_memset.S.obj             12      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1450    0         0      
                                                               
       Stack:                         0       0         256    
       Linker Generated:              0       163       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   26008   1119      1687   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000069ec records: 3, size/record: 8, table size: 24
	.data: load addr=********, load size=0000006f bytes, run addr=202002f0, run size=******** bytes, compression=lzss
	.TrimTable: load addr=000069dc, load size=00000008 bytes, run addr=********, run size=******** bytes, compression=zero_init
	.bss: load addr=000069e4, load size=00000008 bytes, run addr=********, run size=000002ef bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000069d0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000540f  ADC0_IRQHandler                      
0000540f  ADC1_IRQHandler                      
0000540f  AESADV_IRQHandler                    
202002c8  AccumulatedCharge_Frac               
202002cc  AccumulatedCharge_Int                
202002d0  AccumulatedCharge_Time               
00003f9d  BMU_Normal_Mode                      
0000622d  BMU_Ship_Mode                        
000057f1  BMU_Shutdown_Mode                    
000050c1  BMU_Standby_Mode                     
20200534  BOTBatteryStatus                     
20200543  BOTFULLSCANCycle                     
20200542  BOTFULLSCANCycle2                    
20200536  BOTProtectionFlag                    
000000c1  BQ769x2_BOT_Init                     
00005a5d  BQ769x2_ReadAllCellVoltage           
000063c1  BQ769x2_ReadBatteryStatus            
00005e59  BQ769x2_ReadCurrent                  
000048f9  BQ769x2_ReadPassQ                    
000054a9  BQ769x2_ReadSafetyStatus             
00001af1  BQ769x2_TOP_Init                     
00003355  BatteryDataUpdate_32s                
20200538  Battery_status                       
202003e0  BotCellVoltage_cali                  
202004a0  BotCellVoltage_raw                   
00006652  C$$EXIT                              
********  CANDLC_Coding                        
0000603d  CANFD0_IRQHandler                    
00005ee9  CANFD1_IRQHandler                    
000064c9  CANTxMsgSendParamInitDefault         
00003a81  CANTxMsgSendPoll                     
00003921  CAN_ID_Init_on_Startup               
20200544  CAN_Status                           
00003bc9  CAN_Write                            
00004ff5  CANprocessCANRxMsg                   
000052d1  CRC8                                 
20200420  Cell_fixed_offset_LFP                
00006369  CommandSubcommands                   
0000540f  DAC0_IRQHandler                      
0000607d  DL_ADC12_setClockConfig              
00006639  DL_Common_delayCycles                
********  DL_FACTORYREGION_TrimTable           
00005cb1  DL_FactoryRegion_initTrimTable       
00005c51  DL_I2C_fillControllerTXFIFO          
0000613d  DL_I2C_flushControllerTXFIFO         
00006439  DL_I2C_setClockConfig                
0000629d  DL_MCAN_TXBufAddReq                  
00006395  DL_MCAN_addStdMsgIDFilter            
000065b9  DL_MCAN_clearIntrStatus              
00004acd  DL_MCAN_config                       
000064e5  DL_MCAN_enableIntr                   
00006501  DL_MCAN_enableIntrLine               
00006621  DL_MCAN_getIntrStatus                
000065f1  DL_MCAN_getOpMode                    
00005f2d  DL_MCAN_getRevisionId                
00006265  DL_MCAN_getRxFIFOStatus              
00004c95  DL_MCAN_init                         
00006601  DL_MCAN_isMemInitDone                
0000356d  DL_MCAN_msgRAMConfig                 
000042ed  DL_MCAN_readMsgRam                   
0000651d  DL_MCAN_selectIntrLine               
00004d71  DL_MCAN_setBitTime                   
00006461  DL_MCAN_setClockConfig               
000062d1  DL_MCAN_setExtIDAndMask              
00006571  DL_MCAN_setOpMode                    
00004805  DL_MCAN_writeMsgRam                  
000060bd  DL_MCAN_writeRxFIFOAck               
0000470d  DL_SYSCTL_configSYSPLL               
00005659  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005f71  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004505  DL_Timer_initFourCCPWMMode           
000049e5  DL_Timer_initTimerMode               
00006539  DL_Timer_setCaptCompUpdateMethod     
00006589  DL_Timer_setCaptureCompareOutCtl     
00006611  DL_Timer_setCaptureCompareValue      
00006555  DL_Timer_setClockConfig              
00005db9  DL_UART_fillTXFIFO                   
00005ea1  DL_UART_init                         
000064a9  DL_UART_receiveDataBlocking          
000065cd  DL_UART_setClockConfig               
0000540f  DMA_IRQHandler                       
0000540f  Default_Handler                      
20200510  FACTORYVALUE                         
20200545  FET_TEST                             
0000540f  GROUP0_IRQHandler                    
00006339  GROUP1_IRQHandler                    
00005fb5  Gpio_Init                            
00006653  HOSTexit                             
0000540f  HardFault_Handler                    
00004f21  I2C0_IRQHandler                      
0000540f  I2C1_IRQHandler                      
0000540f  I2C2_IRQHandler                      
00003781  I2C_ReadReg                          
202002d4  I2C_TARGET_ADDRESS                   
00005179  I2C_WriteReg                         
0000540f  LFSS_IRQHandler                      
0000540f  NMI_Handler                          
20200546  OCD2_Fault                           
20200547  OV_Fault                             
202002f0  PASSQ_30min                          
20200548  PASSQ_TIME_MIN                       
202002e8  PASSQ_read                           
20200514  PASS_Q                               
2020053a  Pack_Current                         
0000540f  PendSV_Handler                       
20200549  ProtectionsTriggered                 
20200368  Qtime_30min                          
00003e59  RS485_Send                           
202004c0  RX_32Byte                            
2020053c  RX_data                              
00006657  Reset_Handler                        
2020054a  SCD_Fault                            
0000540f  SPI0_IRQHandler                      
0000540f  SPI1_IRQHandler                      
0000540f  SPI2_IRQHandler                      
0000540f  SVC_Handler                          
00005d09  SYSCFG_DL_ADC12_0_init               
00003d11  SYSCFG_DL_GPIO_init                  
00005d61  SYSCFG_DL_I2C_0_init                 
000040c1  SYSCFG_DL_MCAN0_init                 
000041dd  SYSCFG_DL_MCAN1_init                 
0000553d  SYSCFG_DL_PWM_0_init                 
00005975  SYSCFG_DL_SYSCTL_init                
00005caf  SYSCFG_DL_SYSTICK_init               
00005e0d  SYSCFG_DL_TIMER_0_init               
00005411  SYSCFG_DL_UART_0_init                
00005ff9  SYSCFG_DL_init                       
000056e1  SYSCFG_DL_initPower                  
0000540f  SysTick_Handler                      
20200518  Systick_counter                      
00006305  TIMA0_IRQHandler                     
0000540f  TIMA1_IRQHandler                     
0000540f  TIMG0_IRQHandler                     
0000540f  TIMG12_IRQHandler                    
0000540f  TIMG14_IRQHandler                    
0000540f  TIMG6_IRQHandler                     
0000540f  TIMG7_IRQHandler                     
0000540f  TIMG8_IRQHandler                     
0000540f  TIMG9_IRQHandler                     
2020053e  TOPBatteryStatus                     
2020054c  TOPFULLSCANCycle                     
2020054b  TOPFULLSCANCycle2                    
20200540  TOPProtectionFlag                    
20200460  TopCellVoltage_cali                  
202004e0  TopCellVoltage_raw                   
00005225  UART0_IRQHandler                     
0000540f  UART1_IRQHandler                     
0000540f  UART3_IRQHandler                     
0000540f  UART4_IRQHandler                     
0000540f  UART5_IRQHandler                     
0000540f  UART6_IRQHandler                     
0000540f  UART7_IRQHandler                     
2020054d  UV_Fault                             
00005bf1  Variables_Init                       
2020054e  Working_mode                         
********  __STACK_END                          
********  __STACK_SIZE                         
********  __TI_ATRegion0_region_sz             
********  __TI_ATRegion0_src_addr              
********  __TI_ATRegion0_trg_addr              
********  __TI_ATRegion1_region_sz             
********  __TI_ATRegion1_src_addr              
********  __TI_ATRegion1_trg_addr              
********  __TI_ATRegion2_region_sz             
********  __TI_ATRegion2_src_addr              
********  __TI_ATRegion2_trg_addr              
000069ec  __TI_CINIT_Base                      
00006a04  __TI_CINIT_Limit                     
00006a04  __TI_CINIT_Warm                      
000069d0  __TI_Handler_Table_Base              
000069dc  __TI_Handler_Table_Limit             
000061b5  __TI_auto_init_nobinit_nopinit       
000058f9  __TI_decompress_lzss                 
000065df  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
********  __TI_static_base__                   
000065a1  __TI_zero_init_nomemset              
00004e53  __addsf3                             
00005b2d  __aeabi_dcmpeq                       
00005b69  __aeabi_dcmpge                       
00005b7d  __aeabi_dcmpgt                       
00005b55  __aeabi_dcmple                       
00005b41  __aeabi_dcmplt                       
000043f9  __aeabi_ddiv                         
00004bb1  __aeabi_dmul                         
00004e53  __aeabi_fadd                         
000055cd  __aeabi_fmul                         
00004e49  __aeabi_fsub                         
00006179  __aeabi_i2f                          
0000622b  __aeabi_idiv0                        
0000662d  __aeabi_memclr                       
0000662d  __aeabi_memclr4                      
0000662d  __aeabi_memclr8                      
00006645  __aeabi_memcpy                       
00006645  __aeabi_memcpy4                      
00006645  __aeabi_memcpy8                      
00006485  __aeabi_ui2d                         
000063e9  __aeabi_ui2f                         
000060fd  __aeabi_uidiv                        
000060fd  __aeabi_uidivmod                     
ffffffff  __binit__                            
00005ac5  __cmpdf2                             
000043f9  __divdf3                             
00005ac5  __eqdf2                              
00006179  __floatsisf                          
00006485  __floatunsidf                        
000063e9  __floatunsisf                        
000059e9  __gedf2                              
000059e9  __gtdf2                              
00005ac5  __ledf2                              
00005ac5  __ltdf2                              
UNDEFED   __mpu_init                           
00004bb1  __muldf3                             
000061f1  __muldsi3                            
000055cd  __mulsf3                             
00005ac5  __nedf2                              
2020ff00  __stack                              
********  __start___llvm_prf_bits              
********  __start___llvm_prf_cnts              
********  __stop___llvm_prf_bits               
********  __stop___llvm_prf_cnts               
00004e49  __subsf3                             
********  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000665b  _system_pre_init                     
0000664d  abort                                
ffffffff  binit                                
00004609  check_signal_pattern                 
202002e9  deadband                             
00005769  delayUS                              
20200528  gCAN1IntLine1Status                  
2020054f  gCAN1ServiceInt                      
20200550  gCANIDSet                            
2020052c  gCANIntLine1Status                   
20200530  gCANNodeId                           
202002a0  gCANRxFS                             
20200178  gCANRxMsg                            
20200551  gCANServiceInt                       
202001e0  gCANTxMsg                            
202002ea  gI2cControllerStatus                 
********  gPWM_0Backup                         
20200244  gRS485                               
202002d8  gRxCount                             
202002dc  gRxLen                               
202002b8  gRxPacket                            
20200552  gTIMER0                              
202000bc  gTIMER_0Backup                       
202002e0  gTxCount                             
202002e4  gTxLen                               
20200500  gTxPacket                            
20200553  gWAKEINMCU                           
********  interruptVectors                     
00005875  main                                 
00005375  memcpy                               
00005b8f  memset                               
202002eb  send_success                         
202002ec  value_SafetyStatusA                  
202002ed  value_SafetyStatusB                  
202002ee  value_SafetyStatusC                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
********  __TI_ATRegion0_region_sz             
********  __TI_ATRegion0_src_addr              
********  __TI_ATRegion0_trg_addr              
********  __TI_ATRegion1_region_sz             
********  __TI_ATRegion1_src_addr              
********  __TI_ATRegion1_trg_addr              
********  __TI_ATRegion2_region_sz             
********  __TI_ATRegion2_src_addr              
********  __TI_ATRegion2_trg_addr              
********  __TI_static_base__                   
********  interruptVectors                     
000000c1  BQ769x2_BOT_Init                     
********  __STACK_SIZE                         
00001af1  BQ769x2_TOP_Init                     
00003355  BatteryDataUpdate_32s                
0000356d  DL_MCAN_msgRAMConfig                 
00003781  I2C_ReadReg                          
00003921  CAN_ID_Init_on_Startup               
00003a81  CANTxMsgSendPoll                     
00003bc9  CAN_Write                            
00003d11  SYSCFG_DL_GPIO_init                  
00003e59  RS485_Send                           
00003f9d  BMU_Normal_Mode                      
000040c1  SYSCFG_DL_MCAN0_init                 
000041dd  SYSCFG_DL_MCAN1_init                 
000042ed  DL_MCAN_readMsgRam                   
000043f9  __aeabi_ddiv                         
000043f9  __divdf3                             
00004505  DL_Timer_initFourCCPWMMode           
00004609  check_signal_pattern                 
0000470d  DL_SYSCTL_configSYSPLL               
00004805  DL_MCAN_writeMsgRam                  
000048f9  BQ769x2_ReadPassQ                    
000049e5  DL_Timer_initTimerMode               
00004acd  DL_MCAN_config                       
00004bb1  __aeabi_dmul                         
00004bb1  __muldf3                             
00004c95  DL_MCAN_init                         
00004d71  DL_MCAN_setBitTime                   
00004e49  __aeabi_fsub                         
00004e49  __subsf3                             
00004e53  __addsf3                             
00004e53  __aeabi_fadd                         
00004f21  I2C0_IRQHandler                      
00004ff5  CANprocessCANRxMsg                   
000050c1  BMU_Standby_Mode                     
00005179  I2C_WriteReg                         
00005225  UART0_IRQHandler                     
000052d1  CRC8                                 
00005375  memcpy                               
0000540f  ADC0_IRQHandler                      
0000540f  ADC1_IRQHandler                      
0000540f  AESADV_IRQHandler                    
0000540f  DAC0_IRQHandler                      
0000540f  DMA_IRQHandler                       
0000540f  Default_Handler                      
0000540f  GROUP0_IRQHandler                    
0000540f  HardFault_Handler                    
0000540f  I2C1_IRQHandler                      
0000540f  I2C2_IRQHandler                      
0000540f  LFSS_IRQHandler                      
0000540f  NMI_Handler                          
0000540f  PendSV_Handler                       
0000540f  SPI0_IRQHandler                      
0000540f  SPI1_IRQHandler                      
0000540f  SPI2_IRQHandler                      
0000540f  SVC_Handler                          
0000540f  SysTick_Handler                      
0000540f  TIMA1_IRQHandler                     
0000540f  TIMG0_IRQHandler                     
0000540f  TIMG12_IRQHandler                    
0000540f  TIMG14_IRQHandler                    
0000540f  TIMG6_IRQHandler                     
0000540f  TIMG7_IRQHandler                     
0000540f  TIMG8_IRQHandler                     
0000540f  TIMG9_IRQHandler                     
0000540f  UART1_IRQHandler                     
0000540f  UART3_IRQHandler                     
0000540f  UART4_IRQHandler                     
0000540f  UART5_IRQHandler                     
0000540f  UART6_IRQHandler                     
0000540f  UART7_IRQHandler                     
00005411  SYSCFG_DL_UART_0_init                
000054a9  BQ769x2_ReadSafetyStatus             
0000553d  SYSCFG_DL_PWM_0_init                 
000055cd  __aeabi_fmul                         
000055cd  __mulsf3                             
00005659  DL_SYSCTL_setHFCLKSourceHFXTParams   
000056e1  SYSCFG_DL_initPower                  
00005769  delayUS                              
000057f1  BMU_Shutdown_Mode                    
00005875  main                                 
000058f9  __TI_decompress_lzss                 
00005975  SYSCFG_DL_SYSCTL_init                
000059e9  __gedf2                              
000059e9  __gtdf2                              
00005a5d  BQ769x2_ReadAllCellVoltage           
00005ac5  __cmpdf2                             
00005ac5  __eqdf2                              
00005ac5  __ledf2                              
00005ac5  __ltdf2                              
00005ac5  __nedf2                              
00005b2d  __aeabi_dcmpeq                       
00005b41  __aeabi_dcmplt                       
00005b55  __aeabi_dcmple                       
00005b69  __aeabi_dcmpge                       
00005b7d  __aeabi_dcmpgt                       
00005b8f  memset                               
00005bf1  Variables_Init                       
00005c51  DL_I2C_fillControllerTXFIFO          
00005caf  SYSCFG_DL_SYSTICK_init               
00005cb1  DL_FactoryRegion_initTrimTable       
00005d09  SYSCFG_DL_ADC12_0_init               
00005d61  SYSCFG_DL_I2C_0_init                 
00005db9  DL_UART_fillTXFIFO                   
00005e0d  SYSCFG_DL_TIMER_0_init               
00005e59  BQ769x2_ReadCurrent                  
00005ea1  DL_UART_init                         
00005ee9  CANFD1_IRQHandler                    
00005f2d  DL_MCAN_getRevisionId                
00005f71  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00005fb5  Gpio_Init                            
00005ff9  SYSCFG_DL_init                       
0000603d  CANFD0_IRQHandler                    
0000607d  DL_ADC12_setClockConfig              
000060bd  DL_MCAN_writeRxFIFOAck               
000060fd  __aeabi_uidiv                        
000060fd  __aeabi_uidivmod                     
0000613d  DL_I2C_flushControllerTXFIFO         
00006179  __aeabi_i2f                          
00006179  __floatsisf                          
000061b5  __TI_auto_init_nobinit_nopinit       
000061f1  __muldsi3                            
0000622b  __aeabi_idiv0                        
0000622d  BMU_Ship_Mode                        
00006265  DL_MCAN_getRxFIFOStatus              
0000629d  DL_MCAN_TXBufAddReq                  
000062d1  DL_MCAN_setExtIDAndMask              
00006305  TIMA0_IRQHandler                     
00006339  GROUP1_IRQHandler                    
00006369  CommandSubcommands                   
00006395  DL_MCAN_addStdMsgIDFilter            
000063c1  BQ769x2_ReadBatteryStatus            
000063e9  __aeabi_ui2f                         
000063e9  __floatunsisf                        
********  _c_int00_noargs                      
00006439  DL_I2C_setClockConfig                
00006461  DL_MCAN_setClockConfig               
00006485  __aeabi_ui2d                         
00006485  __floatunsidf                        
000064a9  DL_UART_receiveDataBlocking          
000064c9  CANTxMsgSendParamInitDefault         
000064e5  DL_MCAN_enableIntr                   
00006501  DL_MCAN_enableIntrLine               
0000651d  DL_MCAN_selectIntrLine               
00006539  DL_Timer_setCaptCompUpdateMethod     
00006555  DL_Timer_setClockConfig              
00006571  DL_MCAN_setOpMode                    
00006589  DL_Timer_setCaptureCompareOutCtl     
000065a1  __TI_zero_init_nomemset              
000065b9  DL_MCAN_clearIntrStatus              
000065cd  DL_UART_setClockConfig               
000065df  __TI_decompress_none                 
000065f1  DL_MCAN_getOpMode                    
00006601  DL_MCAN_isMemInitDone                
00006611  DL_Timer_setCaptureCompareValue      
00006621  DL_MCAN_getIntrStatus                
0000662d  __aeabi_memclr                       
0000662d  __aeabi_memclr4                      
0000662d  __aeabi_memclr8                      
00006639  DL_Common_delayCycles                
00006645  __aeabi_memcpy                       
00006645  __aeabi_memcpy4                      
00006645  __aeabi_memcpy8                      
0000664d  abort                                
00006652  C$$EXIT                              
00006653  HOSTexit                             
00006657  Reset_Handler                        
0000665b  _system_pre_init                     
********  CANDLC_Coding                        
000069d0  __TI_Handler_Table_Base              
000069dc  __TI_Handler_Table_Limit             
000069ec  __TI_CINIT_Base                      
00006a04  __TI_CINIT_Limit                     
00006a04  __TI_CINIT_Warm                      
********  __start___llvm_prf_bits              
********  __start___llvm_prf_cnts              
********  __stop___llvm_prf_bits               
********  __stop___llvm_prf_cnts               
********  gPWM_0Backup                         
202000bc  gTIMER_0Backup                       
20200178  gCANRxMsg                            
202001e0  gCANTxMsg                            
20200244  gRS485                               
202002a0  gCANRxFS                             
202002b8  gRxPacket                            
202002c8  AccumulatedCharge_Frac               
202002cc  AccumulatedCharge_Int                
202002d0  AccumulatedCharge_Time               
202002d4  I2C_TARGET_ADDRESS                   
202002d8  gRxCount                             
202002dc  gRxLen                               
202002e0  gTxCount                             
202002e4  gTxLen                               
202002e8  PASSQ_read                           
202002e9  deadband                             
202002ea  gI2cControllerStatus                 
202002eb  send_success                         
202002ec  value_SafetyStatusA                  
202002ed  value_SafetyStatusB                  
202002ee  value_SafetyStatusC                  
202002f0  PASSQ_30min                          
20200368  Qtime_30min                          
202003e0  BotCellVoltage_cali                  
20200420  Cell_fixed_offset_LFP                
20200460  TopCellVoltage_cali                  
202004a0  BotCellVoltage_raw                   
202004c0  RX_32Byte                            
202004e0  TopCellVoltage_raw                   
20200500  gTxPacket                            
20200510  FACTORYVALUE                         
20200514  PASS_Q                               
20200518  Systick_counter                      
20200528  gCAN1IntLine1Status                  
2020052c  gCANIntLine1Status                   
20200530  gCANNodeId                           
20200534  BOTBatteryStatus                     
20200536  BOTProtectionFlag                    
20200538  Battery_status                       
2020053a  Pack_Current                         
2020053c  RX_data                              
2020053e  TOPBatteryStatus                     
20200540  TOPProtectionFlag                    
20200542  BOTFULLSCANCycle2                    
20200543  BOTFULLSCANCycle                     
20200544  CAN_Status                           
20200545  FET_TEST                             
20200546  OCD2_Fault                           
20200547  OV_Fault                             
20200548  PASSQ_TIME_MIN                       
20200549  ProtectionsTriggered                 
2020054a  SCD_Fault                            
2020054b  TOPFULLSCANCycle2                    
2020054c  TOPFULLSCANCycle                     
2020054d  UV_Fault                             
2020054e  Working_mode                         
2020054f  gCAN1ServiceInt                      
20200550  gCANIDSet                            
20200551  gCANServiceInt                       
20200552  gTIMER0                              
20200553  gWAKEINMCU                           
********  DL_FACTORYREGION_TrimTable           
2020ff00  __stack                              
********  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[261 symbols]
